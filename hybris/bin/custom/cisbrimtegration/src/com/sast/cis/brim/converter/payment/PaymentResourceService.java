package com.sast.cis.brim.converter.payment;

import com.google.common.base.Preconditions;
import com.sast.cis.brim.converter.data.AdyenSepaDirectDebitTechnicalResources;
import com.sast.cis.brim.converter.data.BoschTransferTechnicalResources;
import com.sast.cis.brim.converter.data.DpgBankTransferTechnicalResources;
import com.sast.cis.brim.converter.data.DpgCreditCardTechnicalResources;
import com.sast.cis.brim.converter.data.EmptyTechnicalResources;
import com.sast.cis.brim.converter.data.PgwSepaDirectDebitTechnicalResources;
import com.sast.cis.brim.converter.data.StripeBankTransferTechnicalResources;
import com.sast.cis.brim.converter.data.StripeCreditCardTechnicalResources;
import com.sast.cis.brim.converter.data.TechnicalResources;
import com.sast.cis.brim.exception.BrimException;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.AchInternationalCreditTransferPaymentInfoModel;
import com.sast.cis.core.model.AchTransferPaymentInstrumentModel;
import com.sast.cis.core.model.InvoiceBySellerPaymentInfoModel;
import com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel;
import com.sast.cis.core.model.SepaMandatePaymentInfoModel;
import com.sast.cis.core.model.SepaTransferPaymentInstrumentModel;
import com.sast.cis.core.model.StripeCreditCardPaymentInfoModel;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.paymentintegration.PaymentTransactionService;
import com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

@Service
@RequiredArgsConstructor
public class PaymentResourceService {
    private final PaymentTransactionService paymentTransactionService;

    public TechnicalResources getTechnicalResources(AbstractOrderModel order) {
        PaymentTransactionModel authorizationTransaction = paymentTransactionService.getAuthorizedTransactionForCurrentPaymentInfo(order)
                .orElseThrow(() -> new BrimException(String.format("Can not determine authorization transaction for order=%s", order.getCode())));

        Preconditions.checkState(authorizationTransaction.getInfo() != null,
                "Found payment transaction(code=%s) has no payment info", authorizationTransaction.getCode());
        PaymentProvider paymentProvider = PaymentProvider.valueOf(authorizationTransaction.getPaymentProvider());


        return switch (paymentProvider) {
            case STRIPE -> getStripeResources(authorizationTransaction);
            case DPG -> getDpgResources(authorizationTransaction);
            case ZERO -> getZeroResources(authorizationTransaction);
            case BOSCH_TRANSFER -> getBoschTransferResources(authorizationTransaction);
            case PGW -> getPGWTechnicalResources(authorizationTransaction);
            case ADYEN -> getAdyenTechnicalResources(authorizationTransaction);
        };
    }

    private TechnicalResources getStripeResources(PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();

        if (paymentInfo instanceof SepaCreditTransferPaymentInfoModel) {
            SepaTransferPaymentInstrumentModel sepaTransferInstrument =
                    (SepaTransferPaymentInstrumentModel) paymentTransaction.getPaymentInstrument();
            return new StripeBankTransferTechnicalResources()
                    .setSourceId(paymentTransaction.getRequestToken())
                    .setBankName(sepaTransferInstrument.getBankName())
                    .setIban(sepaTransferInstrument.getIban())
                    .setBic(sepaTransferInstrument.getBic())
                    .setCustomerId(sepaTransferInstrument.getPspCustomerIdentifier());
        } else if (paymentInfo instanceof StripeCreditCardPaymentInfoModel) {
            Preconditions.checkState(StringUtils.isNotBlank(paymentTransaction.getRequestToken()),
                    "Missing required attribute requestToken in payment transaction %s", paymentTransaction.getCode());
            return new StripeCreditCardTechnicalResources()
                    .setPaymentIntentId(paymentTransaction.getRequestToken());
        } else {
            throw new IllegalStateException(
                    String.format("Given unsupported payment info type %s for provider STRIPE.", paymentInfo.getClass().getSimpleName()));
        }
    }

    private TechnicalResources getDpgResources(PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();

        if (paymentInfo instanceof SepaCreditTransferPaymentInfoModel) {
            SepaTransferPaymentInstrumentModel paymentInstrument = (SepaTransferPaymentInstrumentModel) paymentTransaction
                    .getPaymentInstrument();
            return new DpgBankTransferTechnicalResources()
                    .setAccountNumber(paymentInstrument.getIban())
                    .setBic(paymentInstrument.getBic())
                    .setBankName(paymentInstrument.getBankName());
        } else if (paymentInfo instanceof AchInternationalCreditTransferPaymentInfoModel) {
            AchTransferPaymentInstrumentModel paymentInstrument = (AchTransferPaymentInstrumentModel) paymentTransaction
                    .getPaymentInstrument();
            return new DpgBankTransferTechnicalResources()
                    .setBankName(paymentInstrument.getBankName())
                    .setRoutingNumber(paymentInstrument.getRoutingNumber())
                    .setAccountNumber(paymentInstrument.getAccountNumber())
                    .setBic(paymentInstrument.getBic());
        } else if (paymentInfo instanceof DpgCreditCardPaymentInfoModel dpgCreditCardPaymentInfo) {
            return new DpgCreditCardTechnicalResources()
                    .setPaymentInstrumentId(dpgCreditCardPaymentInfo.getPaymentInstrumentId())
                    .setPurchaseId(paymentTransaction.getRequestToken())
                    .setSequenceReference(getAcceptedAuthorizationEntry(paymentTransaction).getSubscriptionID());
        } else {
            throw new IllegalStateException(
                    String.format("Given unsupported payment info type %s for provider DPG.", paymentInfo.getClass().getSimpleName()));
        }
    }

    private TechnicalResources getZeroResources(PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();

        if (paymentInfo instanceof InvoiceBySellerPaymentInfoModel) {
            return new EmptyTechnicalResources();
        } else {
            throw new IllegalStateException(
                    String.format("Given unsupported payment info type %s for provider ZERO.", paymentInfo.getClass().getSimpleName()));
        }
    }

    private TechnicalResources getBoschTransferResources(PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();

        if (paymentInfo instanceof SepaCreditTransferPaymentInfoModel) {
            SepaTransferPaymentInstrumentModel paymentInstrument =
                    (SepaTransferPaymentInstrumentModel) paymentTransaction.getPaymentInstrument();
            return new BoschTransferTechnicalResources()
                    .setBankAccountNumber(paymentInstrument.getIban())
                    .setBic(paymentInstrument.getBic())
                    .setBankName(paymentInstrument.getBankName());
        } else {
            throw new IllegalStateException(
                    String.format("Given unsupported payment info type %s for provider BOSCH_TRANSFER.",
                            paymentInfo.getClass().getSimpleName()));
        }
    }

    private TechnicalResources getPGWTechnicalResources(PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();
        if (paymentInfo instanceof SepaMandatePaymentInfoModel sepaMandateInfo) {
            Preconditions.checkState(sepaMandateInfo.getDateOfSignature() != null,
                    "Payment info %s has no date of signature", sepaMandateInfo.getCode());
            Preconditions.checkState(StringUtils.isNotBlank(sepaMandateInfo.getAccountHolderName()),
                    "Payment info %s has no account holder name");
            Preconditions.checkState(StringUtils.isNotBlank(sepaMandateInfo.getIBAN()),
                    "Payment info %s has no IBAN", sepaMandateInfo.getIBAN());
            Preconditions.checkState(StringUtils.isNotBlank(sepaMandateInfo.getMandateReference()),
                    "Payment info %s has no mandate reference", sepaMandateInfo.getMandateReference());
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            return new PgwSepaDirectDebitTechnicalResources()
                    .setAccountHolderName(sepaMandateInfo.getAccountHolderName())
                    .setDateOfSignature(df.format(sepaMandateInfo.getDateOfSignature()))
                    .setIban(sepaMandateInfo.getIBAN())
                    .setMandateReference(sepaMandateInfo.getMandateReference());
        } else {
            throw new IllegalStateException(
                    String.format("Given unsupported payment info type %s for provider PGW.",
                            paymentInfo.getClass().getSimpleName()));
        }
    }

    private TechnicalResources getAdyenTechnicalResources(final PaymentTransactionModel paymentTransaction) {
        PaymentInfoModel paymentInfo = paymentTransaction.getInfo();
        if (paymentInfo instanceof TokenizedSepaDirectDebitPaymentInfoModel adyenSepaPaymentInfo) {
            Preconditions.checkState(StringUtils.isNotBlank(adyenSepaPaymentInfo.getIBAN()),
                "Payment info %s has no IBAN", adyenSepaPaymentInfo.getCode());
            Preconditions.checkState(StringUtils.isNotBlank(adyenSepaPaymentInfo.getMandateReference()),
                "Payment info %s has no mandate reference", adyenSepaPaymentInfo.getCode());
            Preconditions.checkState(StringUtils.isNotBlank(adyenSepaPaymentInfo.getShopperReference()),
                "Payment info %s has no shopper reference", adyenSepaPaymentInfo.getCode());
            Preconditions.checkState(StringUtils.isNotBlank(adyenSepaPaymentInfo.getRecurringReference()),
                "Payment info %s has no recurring reference", adyenSepaPaymentInfo.getRecurringReference());

            return new AdyenSepaDirectDebitTechnicalResources()
                .setIban(adyenSepaPaymentInfo.getIBAN())
                .setPspReference(adyenSepaPaymentInfo.getPspTokenizationReference())
                .setShopperReference(adyenSepaPaymentInfo.getShopperReference())
                .setPaymentMandate(adyenSepaPaymentInfo.getMandateReference())
                .setRecurringReference(adyenSepaPaymentInfo.getRecurringReference());
        } else {
            throw new IllegalStateException(
                String.format("Given unsupported payment info type %s for provider ADYEN.",
                    paymentInfo.getClass().getSimpleName()));
        }
    }
    private PaymentTransactionEntryModel getAcceptedAuthorizationEntry(PaymentTransactionModel paymentTransaction) {
        return CollectionUtils.emptyIfNull(paymentTransaction.getEntries()).stream()
                .filter(transactionEntry -> PaymentTransactionType.AUTHORIZATION.equals(transactionEntry.getType()))
                .filter(transactionEntry -> TransactionStatus.ACCEPTED.toString().equals(transactionEntry.getTransactionStatus()))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("Given payment transaction does not have an accepted authorization entry"));
    }
}
