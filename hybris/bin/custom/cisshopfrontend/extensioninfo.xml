<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
 [y] hybris Platform

 Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<extensioninfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="extensioninfo.xsd">
	<extension abstractclassprefix="Generated" classprefix="Cisshopfrontend" jaloLogicFree="true" managername="CisshopfrontendManager" managersuperclass="de.hybris.platform.jalo.extension.Extension" name="cisshopfrontend" usemaven="false">
	    <requires-extension name="ciscore"/>
		<requires-extension name="ciswebaddon"/>
		<requires-extension name="acceleratorstorefrontcommons"/>
		<requires-extension name="cisaccfacades"/>
		<requires-extension name="webservicescommons"/>
		<requires-extension name="cisemail2"/>
		<requires-extension name="ciscompanyprofile"/>
		<requires-extension name="cismmsintegration"/>

		<coremodule generated="true" manager="com.sast.cis.shop.frontend.jalo.CisshopfrontendManager" packageroot="com.sast.cis.shop.frontend"/>
		<webmodule jspcompile="false" webroot="/cisshopfrontend"/>

		<meta key="extgen-template-extension" value="true"/>
	</extension>
</extensioninfo>
