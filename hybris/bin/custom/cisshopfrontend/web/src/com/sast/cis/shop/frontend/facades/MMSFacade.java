package com.sast.cis.shop.frontend.facades;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import com.sast.cis.mms.integration.service.MandateManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MMSFacade {

    private final MandateManagementService mandateManagementService;
    private final IotCompanyService companyService;

    public SepaMandateDto createDraftMandate() {
        final IoTCompanyModel currentCompany = companyService.getCurrentCompanyOrThrow();
        return mandateManagementService.createDraftMandate(currentCompany);
    }

    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto mandateDto) {
        return mandateManagementService.finalizeDraftMandate(reference, mandateDto);
    }


}
