package com.sast.cis.shop.frontend.payment;

import com.sast.cis.core.data.CreditCardPaymentInfoData;
import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.data.SepaMandatePaymentInfoData;
import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.shop.frontend.form.PaymentForm;
import de.hybris.platform.commercefacades.user.data.AddressData;
import lombok.NonNull;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.google.common.base.Preconditions.checkArgument;
import static com.sast.cis.core.enums.PaymentProvider.DPG;

public class PaymentInfoDataFactory {

    private static final Pattern DPG_EXPIRY_PATTERN = Pattern.compile("^(?<expiryMonth>\\d{2})/(?<expiryYear>\\d{2})$");

    public static PaymentInfoData createPaymentInfoData(@NonNull final PaymentForm paymentForm) {
        return createPaymentInfoData(paymentForm, null);
    }

    public static PaymentInfoData createPaymentInfoData(@NonNull final PaymentForm paymentForm, final AddressData billingAddress) {
        final PaymentMethodType paymentMethod = PaymentMethodType.valueOf(paymentForm.getPaymentMethod());
        final PaymentProvider paymentProvider = PaymentProvider.valueOf(paymentForm.getPaymentProvider());

        final PaymentInfoData paymentInfo = switch (paymentMethod) {
            case SEPA_DIRECTDEBIT -> createSepaPaymentInfoData(paymentForm, paymentProvider);
            case CREDIT_CARD -> createCreditCardPaymentInfoData(paymentForm, paymentProvider, billingAddress);
            default -> new PaymentInfoData();
        };

        paymentInfo.setPaymentProvider(paymentProvider);
        paymentInfo.setReusable(paymentForm.isSaveCard());
        paymentInfo.setPaymentMethod(paymentMethod);

        return paymentInfo;
    }

    private static PaymentInfoData createSepaPaymentInfoData(final PaymentForm paymentForm, final PaymentProvider paymentProvider) {
        return switch (paymentProvider) {
            case PGW -> new SepaMandatePaymentInfoData();
            case ADYEN -> new TokenizedSepaDirectDebitPaymentInfoData()
                .withIban(paymentForm.getIban())
                .withAccountHolderName(paymentForm.getAccountHolder());
            default -> throw new IllegalStateException("Direct Debit not supported for '%s'".formatted(paymentProvider));

        };
    }

    private static PaymentInfoData createCreditCardPaymentInfoData(
        final PaymentForm paymentForm,
        final PaymentProvider paymentProvider,
        final AddressData billingAddress) {

        checkArgument(DPG.equals(paymentProvider), "Credit card is only supported for DPG.");

        final Matcher matcher = DPG_EXPIRY_PATTERN.matcher(paymentForm.getExpiry());
        checkArgument(matcher.matches(), "Given expiry date does not match pattern MM/YY.");

        return new CreditCardPaymentInfoData()
            .withBillingAddress(billingAddress)
            .withSubscriptionId(paymentForm.getExternalPaymentMethodId())
            .withAccountHolderName(paymentForm.getOwner())
            .withToken(paymentForm.getToken())
            .withExpiryMonth(matcher.group("expiryMonth"))
            .withExpiryYear(matcher.group("expiryYear"))
            .withCardNumber(paymentForm.getTruncatedCardNumber());
    }
}
