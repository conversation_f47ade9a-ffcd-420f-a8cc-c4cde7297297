package com.sast.cis.shop.frontend.controllers.rest;

import com.sast.cis.mms.integration.dto.SepaMandateDto;
import com.sast.cis.mms.integration.service.MandateManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * REST controller for SEPA mandate operations.
 * This controller acts as a proxy between the frontend and the sepa-mandate service,
 * ensuring proper authentication and authorization.
 */
@Slf4j
@RestController
@RequestMapping("/sepa-mandates")
@PreAuthorize("@userPermissionService.hasShopPermission('EDIT_PAYMENT_DETAILS')")
@RequiredArgsConstructor
@Api(tags = "SEPA Mandates")
public class SepaMandateResource {

    private final MandateManagementService mandateManagementService;

    /**
     * Initialize a draft SEPA mandate
     *
     * @param companyId - The company ID for the mandate
     * @return Response with SepaMandateDto containing auto-generated mandate reference
     */
    @PostMapping("/initialize")
    @ApiOperation(value = "Initialize a draft SEPA mandate")
    public SepaMandateDto initializeSepaMandateDraft(
            @RequestParam @NotNull String companyId) {

        LOG.info("REST: Initializing SEPA mandate draft for company: {}", companyId);
        return mandateManagementService.createDraftMandate(companyId);
    }

    /**
     * Update or finalize a SEPA mandate by reference
     *
     * @param reference - The mandate reference
     * @param payload   - The mandate data (can be partial for updates or complete for finalization)
     * @return Response with updated/finalized SepaMandateDto
     */
    @PostMapping("/{reference}/activate")
    @ApiOperation(value = "Update or finalize a SEPA mandate by reference")
    public SepaMandateDto updateOrFinalizeSepaMandateByReference(
            @PathVariable @NotNull String reference,
            @RequestBody @NotNull SepaMandateDto payload) {

        LOG.info("Updating/finalizing SEPA mandate with reference: {}", reference);
        return mandateManagementService.finalizeDraftMandate(reference, payload);
    }
}
