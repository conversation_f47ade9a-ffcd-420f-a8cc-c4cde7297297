package com.sast.cis.shop.frontend.handlers;

import com.sast.cis.core.constants.shop.ShopErrorCode;
import com.sast.cis.core.data.StoreErrorResponseData;
import com.sast.cis.core.exceptions.permission.AppPermissionException;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.InternalServerException;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.service.license.activation.LicenseActivationException;
import com.sast.cis.core.validator.license.activation.LicenseActivationValidationException;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.NoSuchElementException;

import static com.sast.cis.core.constants.shop.ShopErrorCode.*;

@RestControllerAdvice
@RequiredArgsConstructor
public class HttpExceptionHandler extends ResponseEntityExceptionHandler {
    private static final Logger LOG = LoggerFactory.getLogger(HttpExceptionHandler.class);

    private final UserMessageFactory userMessageFactory;

    @ExceptionHandler({WebApplicationException.class})
    public ResponseEntity<Object> handleWebApplicationException(WebApplicationException exception, WebRequest request) {
        Response response = exception.getResponse();
        HttpStatus httpStatus = HttpStatus.valueOf(response.getStatus());
        Object body;
        try {
            body = response.hasEntity() ? response.getEntity() : null;
        } catch (IllegalStateException e) {
            body = null;
            LOG.warn("WebApplicationException with closed response, not adding body. Status: {}", response.getStatusInfo());
        }

        return handleExceptionInternal(exception, body, HttpHeaders.EMPTY, httpStatus, request);
    }

    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public StoreErrorResponseData handleBadRequestException(BadRequestException exc) {
        return createErrorResponsePayload(ShopErrorCode.GENERIC_BAD_REQUEST).withErrorMessage(exc.getMessage());
    }

    @ExceptionHandler({ NotFoundException.class, NoSuchElementException.class, UnknownIdentifierException.class })
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public StoreErrorResponseData handleNotFoundException(final Exception exc) {
        LOG.debug("handleNotFoundException", exc);
        return createErrorResponsePayload(GENERIC_NOT_FOUND).withErrorMessage(exc.getMessage());
    }

    @ExceptionHandler(LicenseActivationValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public StoreErrorResponseData handleLicenseActivationValidationException(final LicenseActivationValidationException exception) {
        var userMessages = CollectionUtils.emptyIfNull(exception.getErrors())
                .stream().map(validationError -> userMessageFactory.error(validationError.getValue())).toList();
        return new StoreErrorResponseData().withUserMessages(userMessages);
    }

    @ExceptionHandler(LicenseActivationException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public StoreErrorResponseData handleLicenseActivationValidationException(final LicenseActivationException exception) {
        return createErrorResponsePayload(ShopErrorCode.LICENSEACTIVATION_ERROR).withErrorMessage(exception.getMessage());
    }

    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler({ AccessDeniedException.class })
    public StoreErrorResponseData accessDeniedExceptionHandler(final AccessDeniedException exception) {
        return createErrorResponsePayload(GENERIC_FORBIDDEN).withErrorMessage(exception.getMessage());
    }

    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ExceptionHandler({ AppPermissionException.class })
    public StoreErrorResponseData appPermissionExceptionHandler(final AppPermissionException exception) {
        LOG.debug("appPermissionExceptionHandler", exception);
        return createErrorResponsePayload(PRODUCT_NOT_FOUND).withErrorMessage(exception.getMessage());
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({ RuntimeException.class, InternalServerException.class })
    public StoreErrorResponseData runtimeExceptionCatchall(final RuntimeException exception) {
        LOG.error("Internal server error:", exception);
        return createErrorResponsePayload(GENERIC_SERVER_ERROR).withErrorMessage(exception.getMessage());
    }

    private StoreErrorResponseData createErrorResponsePayload(ShopErrorCode shopErrorCode) {
        var userMessage = userMessageFactory.error(shopErrorCode);
        return new StoreErrorResponseData()
                .withErrorCode(shopErrorCode.getValue())
                .withUserMessages(userMessage != null ? List.of(userMessage) : List.of());
    }
}
