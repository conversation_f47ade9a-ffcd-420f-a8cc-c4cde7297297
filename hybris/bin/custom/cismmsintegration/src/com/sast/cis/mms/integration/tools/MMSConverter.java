package com.sast.cis.mms.integration.tools;

import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;


public class MMSConverter {

    public static SepaMandateDto toDto(SepaMandateApiDto apiDto) {
        if (apiDto == null) {
            return null;
        }

        return SepaMandateDto.builder()
                .mandateReference(apiDto.getMandateReference())
                .companyId(apiDto.getCompanyId())
                .accountHolderName(apiDto.getAccountHolderName())
                .iban(apiDto.getIban())
                .signatureDate(apiDto.getSignatureDate())
                .status(apiDto.getStatus())
                .build();
    }


    public static SepaMandateApiDto toApiDto(SepaMandateDto dto) {
        if (dto == null) {
            return null;
        }

        return SepaMandateApiDto.builder()
                .mandateReference(dto.getMandateReference())
                .companyId(dto.getCompanyId())
                .accountHolderName(dto.getAccountHolderName())
                .iban(dto.getIban())
                .signatureDate(dto.getSignatureDate())
                .status(dto.getStatus())
                .build();
    }


}