package com.sast.cis.mms.integration.client;

import com.sast.cis.mms.integration.client.api.SepaMandateApi;
import com.sast.cis.mms.integration.client.authentication.config.MMSGatewayConfigProvider;
import com.sast.cis.mms.integration.client.authentication.filter.MMSAuthenticationFilter;
import com.sast.cis.mms.integration.client.configuration.MMSClientJsonResolver;
import com.sast.cis.mms.integration.service.MMSApiImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.logging.LoggingFeature;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Factory for creating SEPA mandate API service clients.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MMSGatewayApiServiceFactory {

    private final MMSAuthenticationFilter mmsAuthenticationFilter;
    private final MMSGatewayConfigProvider mmsGatewayConfigProvider;
    private final MMSClientJsonResolver mmsClientJsonResolver;

    public SepaMandateApi getSepaMandateApi() {
        return new MMSApiImpl(this);
    }

    public WebTarget getSepaMandateGatewayTarget() {
        return getSepaMandateGatewayClient().target(mmsGatewayConfigProvider.getConfig().apiUrl());
    }

    private Client getSepaMandateGatewayClient() {
        return ClientBuilder.newClient()
                .register(getLoggingFeature())
                .register(mmsAuthenticationFilter)
                .register(mmsClientJsonResolver);
    }

    private LoggingFeature getLoggingFeature() {
        return new LoggingFeature(
                Logger.getLogger(LoggingFeature.DEFAULT_LOGGER_NAME),
                Level.INFO, LoggingFeature.DEFAULT_VERBOSITY, 10000
        );
    }

}
