package com.sast.cis.mms.integration.client.authentication;

import com.sast.cis.mms.integration.client.authentication.config.MMSGatewayConfigProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.stereotype.Component;

@Component("mmsAccessTokenProvider")
@RequiredArgsConstructor
public class MMSAccessTokenProvider {

    private final OAuth2AuthorizedClientManager sepaAuthorizedClientManager;
    private final MMSGatewayConfigProvider MMSGatewayConfigProvider;

    public String getAccessToken() {
        final String clientRegistrationId = MMSGatewayConfigProvider.getConfig().authenticationConfig().clientRegistrationId();
        final OAuth2AuthorizeRequest authorizeRequest = OAuth2AuthorizeRequest
            .withClientRegistrationId(clientRegistrationId)
            .principal("system")
            .build();

        final OAuth2AuthorizedClient authorizedClient = sepaAuthorizedClientManager.authorize(authorizeRequest);

        if (authorizedClient == null || authorizedClient.getAccessToken() == null) {
            throw new IllegalStateException("Failed to obtain access token for client: " + clientRegistrationId);
        }

        return authorizedClient.getAccessToken().getTokenValue();
    }
}
