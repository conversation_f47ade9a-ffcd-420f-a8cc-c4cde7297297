package com.sast.cis.mms.integration.client.authentication.config;

import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@RequiredArgsConstructor
@Component
public class MMSGatewayConfigProvider {

    public static final String SEPA_MANDATE_API_URL = "sepa.mandate.api.url";
    public static final String SEPA_MANDATE_AUTH_TOKEN_ENDPOINT_URL = "sepa.mandate.auth.token.endpoint.url";
    public static final String SEPA_MANDATE_AUTH_CLIENT_ID = "sepa.mandate.auth.client.id";
    public static final String SEPA_MANDATE_AUTH_CLIENT_SECRET = "sepa.mandate.auth.client.secret";
    public static final String SEPA_MANDATE_AUTH_CLIENT_REGISTRATION_ID = "sepa.mandate.auth.client.registration.id";

    private static final String DEFAULT_CLIENT_REGISTRATION_ID = "baam-sepa-client";

    private final ConfigurationService configurationService;

    public MMSConfig getConfig() {
        final String apiUrl = getProperty(SEPA_MANDATE_API_URL);

        final String tokenEndpointUrl = getProperty(SEPA_MANDATE_AUTH_TOKEN_ENDPOINT_URL);
        final String clientId = getProperty(SEPA_MANDATE_AUTH_CLIENT_ID);
        final String clientSecret = getProperty(SEPA_MANDATE_AUTH_CLIENT_SECRET);
        final String clientRegistrationId = getProperty(
            SEPA_MANDATE_AUTH_CLIENT_REGISTRATION_ID, DEFAULT_CLIENT_REGISTRATION_ID
        );

        return new MMSConfig(
            apiUrl,
            new MMSConfig.AuthenticationConfig(tokenEndpointUrl, clientId, clientSecret, clientRegistrationId)
        );
    }

    private String getProperty(final String propertyKey) {
        return getProperty(propertyKey, EMPTY);
    }

    private String getProperty(final String propertyKey, final String defaultValue) {
        final String propertyValue = configurationService.getConfiguration().getString(propertyKey, defaultValue);
        if (StringUtils.isBlank(propertyValue)) {
            log.error("ALERT: Property with key '{}' not found", propertyKey);
        }
        return propertyValue;
    }
}
