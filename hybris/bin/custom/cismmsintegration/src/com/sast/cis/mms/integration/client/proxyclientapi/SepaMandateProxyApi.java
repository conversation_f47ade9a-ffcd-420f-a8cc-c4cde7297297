package com.sast.cis.mms.integration.client.proxyclientapi;

import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;

import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * JAX-RS proxy client API for SEPA mandate operations.
 * This interface uses JAX-RS annotations for client-side proxy generation.
 */
@Path("/api/v1/mandates")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface SepaMandateProxyApi {

    /**
     * Create a draft mandate on user attempt to create a mandate (automatically generating the reference)
     * <p>
     * If a mandate already exists for the companyId, return the existing mandate
     */
    @POST
    @Path("/initialize")
    SepaMandateApiDto createMandate(@QueryParam("companyId") String companyId);

    /**
     * If the provided dto contains all required fields, then finalize the draft mandate.
     * <p>
     * Otherwise, update the draft mandate with additional input.
     * <p>
     * this can be ongoing process, and while in progress the mandate will be in draft status.
     */
    @POST
    @Path("/{reference}/activate")
    SepaMandateApiDto activateMandate(@PathParam("reference") String reference, @Valid SepaMandateApiDto dto);

    @GET
    @Path("/{reference}")
    SepaMandateApiDto getMandateByReference(@PathParam("reference") String reference,
                                           @QueryParam("includeDrafts") boolean includeDrafts);

    @GET
    List<SepaMandateApiDto> getAllMandates(@QueryParam("includeDrafts") boolean includeDrafts);
}
