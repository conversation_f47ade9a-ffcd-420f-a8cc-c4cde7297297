package com.sast.cis.mms.integration.service;

import com.sast.cis.mms.integration.client.MMSGatewayApiServiceFactory;
import com.sast.cis.mms.integration.client.api.SepaMandateApi;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class MMSApiImpl implements SepaMandateApi {

    private final MMSGatewayApiServiceFactory factory;

    @Override
    public ResponseEntity<SepaMandateApiDto> createMandate(String companyId) {
        try {
            WebTarget target = factory.getSepaMandateGatewayTarget();
            Response response = target
                    .path("/api/v1/mandates/initialize")
                    .queryParam("companyId", companyId)
                    .request(MediaType.APPLICATION_JSON)
                    .post(Entity.json(""));

            return handleSingleEntityResponse(response, SepaMandateApiDto.class);
        } catch (Exception e) {
            log.error("Error in createMandate for companyId: {}", companyId, e);
            throw new RuntimeException("Failed to create mandate", e);
        }
    }

    @Override
    public ResponseEntity<SepaMandateApiDto> activateMandate(String reference, SepaMandateApiDto dto) {
        try {
            WebTarget target = factory.getSepaMandateGatewayTarget();
            Response response = target
                    .path("/api/v1/mandates/{reference}/activate")
                    .resolveTemplate("reference", reference)
                    .request(MediaType.APPLICATION_JSON)
                    .post(Entity.json(dto != null ? dto : new SepaMandateApiDto()));

            return handleSingleEntityResponse(response, SepaMandateApiDto.class);
        } catch (Exception e) {
            log.error("Error in activateMandate for reference: {}", reference, e);
            throw new RuntimeException("Failed to activate mandate", e);
        }
    }

    @Override
    public ResponseEntity<SepaMandateApiDto> getMandateByReference(String reference, boolean includeDrafts) {
        try {
            WebTarget target = factory.getSepaMandateGatewayTarget();
            Response response = target
                    .path("/api/v1/mandates/{reference}")
                    .resolveTemplate("reference", reference)
                    .queryParam("includeDrafts", includeDrafts)
                    .request(MediaType.APPLICATION_JSON)
                    .get();

            return handleSingleEntityResponse(response, SepaMandateApiDto.class);
        } catch (Exception e) {
            log.error("Error in getMandateByReference for reference: {}", reference, e);
            throw new RuntimeException("Failed to get mandate", e);
        }
    }

    @Override
    public ResponseEntity<List<SepaMandateApiDto>> getAllMandates(boolean includeDrafts) {
        try {
            WebTarget target = factory.getSepaMandateGatewayTarget();
            Response response = target
                    .path("/api/v1/mandates")
                    .queryParam("includeDrafts", includeDrafts)
                    .request(MediaType.APPLICATION_JSON)
                    .get();

            return handleListResponse(response, new GenericType<List<SepaMandateApiDto>>() {});
        } catch (Exception e) {
            log.error("Error in getAllMandates", e);
            throw new RuntimeException("Failed to get all mandates", e);
        }
    }

    private <T> ResponseEntity<T> handleSingleEntityResponse(Response response, Class<T> entityClass) {
        try {
            HttpStatus status = HttpStatus.valueOf(response.getStatus());

            if (response.getStatus() == 404) {
                return ResponseEntity.notFound().build();
            }

            if (response.getStatus() >= 200 && response.getStatus() < 300) {
                T entity = response.readEntity(entityClass);
                return ResponseEntity.status(status).body(entity);
            } else {
                log.error("HTTP error response: {}", response.getStatus());
                return ResponseEntity.status(status).build();
            }
        } finally {
            response.close();
        }
    }

    private <T> ResponseEntity<T> handleListResponse(Response response, GenericType<T> genericType) {
        try {
            HttpStatus status = HttpStatus.valueOf(response.getStatus());

            if (response.getStatus() >= 200 && response.getStatus() < 300) {
                T entity = response.readEntity(genericType);
                return ResponseEntity.status(status).body(entity);
            } else {
                log.error("HTTP error response: {}", response.getStatus());
                return ResponseEntity.status(status).build();
            }
        } finally {
            response.close();
        }
    }
}