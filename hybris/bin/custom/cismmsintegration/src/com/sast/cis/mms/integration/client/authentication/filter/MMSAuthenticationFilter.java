package com.sast.cis.mms.integration.client.authentication.filter;

import com.sast.cis.mms.integration.client.authentication.MMSAccessTokenProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.ws.rs.client.ClientRequestContext;
import javax.ws.rs.client.ClientRequestFilter;

@RequiredArgsConstructor
@Component
public class MMSAuthenticationFilter implements ClientRequestFilter {

    private final MMSAccessTokenProvider MMSAccessTokenProvider;

    @Override
    public void filter(final ClientRequestContext requestContext) {
        final String token = MMSAccessTokenProvider.getAccessToken();
        requestContext.getHeaders().add("Authorization", "Bearer " + token);
    }
}
