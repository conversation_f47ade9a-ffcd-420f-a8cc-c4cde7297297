package com.sast.cis.mms.integration.service;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.mms.integration.client.MMSGatewayApiServiceFactory;
import com.sast.cis.mms.integration.client.api.SepaMandateApi;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import com.sast.cis.mms.integration.tools.MMSConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

/**
 * Service for SEPA mandate operations
 * This service acts as a bridge between the application and the sepa-mandate service,
 * using the SepaMandateGatewayApiServiceFactory to make authenticated REST calls
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MandateManagementService {

    private final MMSGatewayApiServiceFactory MMSGatewayApiServiceFactory;
    private final IotCompanyService iotCompanyService;

    public SepaMandateDto createDraftMandate(String companyUid) {
        log.info("Creating draft mandate for company: {}", companyUid);
        
        try {
            validateCompanyUid(companyUid);

            SepaMandateApi api = MMSGatewayApiServiceFactory.getSepaMandateApi();
            ResponseEntity<SepaMandateApiDto> response = api.createMandate(companyUid);

            if (response.getStatusCodeValue() == OK.value() || response.getStatusCodeValue() == CREATED.value()) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto mandate = MMSConverter.toDto(apiDto);
                log.info("Successfully created draft mandate with reference: {} for company: {}",
                        mandate.getMandateReference(), companyUid);
                return mandate;
            } else {
                log.error("Failed to create draft mandate for company: {}. Status: {}", companyUid, response.getStatusCode());
                throw new RuntimeException("Failed to create draft mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error creating draft mandate for company: {}", companyUid, e);
            throw new RuntimeException("Failed to create draft mandate", e);
        }
    }

    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto mandateDto) {
        log.info("Finalizing draft mandate with reference: {}", reference);

        try {
            SepaMandateApi api = MMSGatewayApiServiceFactory.getSepaMandateApi();
            SepaMandateApiDto payload = MMSConverter.toApiDto(mandateDto);
            ResponseEntity<SepaMandateApiDto> response = api.activateMandate(reference, payload);

            if (response.getStatusCodeValue() == OK.value()) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto finalizedMandate = MMSConverter.toDto(apiDto);
                log.info("Successfully finalized mandate with reference: {}", reference);
                return finalizedMandate;
            } else {
                log.error("Failed to finalize mandate with reference: {}. Status: {}", reference, response.getStatusCode());
                throw new RuntimeException("Failed to finalize mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error finalizing mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to finalize mandate", e);
        }
    }

    /**
     * Validates that the provided company ID matches the current user's company uid
     * This ensures that users can only create mandates for their own company.
     */
    private void validateCompanyUid(String companyUid) {

        if (isBlank(companyUid)) {
            log.warn("Company UID validation failed: it's null or blank");
            throw new IllegalArgumentException("Company UID cannot be null or blank");
        }

        Optional<IoTCompanyModel> currentCompanyOpt = iotCompanyService.getCurrentCompany();
        
        if (currentCompanyOpt.isEmpty()) {
            log.error("Company ID validation failed: No current company found for user");
            throw new IllegalStateException("No current company found for the authenticated user");
        }

        IoTCompanyModel currentCompany = currentCompanyOpt.get();
        String currentCompanyUid = currentCompany.getUid();
        
        // validate that the provided company UID matches the current user's company
        if (!companyUid.equals(currentCompanyUid)) {
            log.warn("Company UID validation failed: Provided company UID '{}' does not match current company '{}'",
                    companyUid, currentCompanyUid);
            throw new SecurityException(String.format(
                    "Cannot create mandate for company uid: '%s'. User is associated with company '%s'",
                    companyUid, currentCompanyUid));
        }
        
        log.debug("Validation successful for companyUid: {}", companyUid);
    }
}
