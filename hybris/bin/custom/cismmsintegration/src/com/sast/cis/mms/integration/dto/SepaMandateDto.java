package com.sast.cis.mms.integration.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sast.cis.mms.integration.client.api.MandateStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Data Transfer Object for SEPA mandate operations.
 * This DTO represents the structure of SEPA mandate data exchanged with the sepa-mandate service
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SepaMandateDto {

    @JsonProperty("mandateReference")
    private String mandateReference;

    @JsonProperty("companyId")
    private String companyId;

    @JsonProperty("accountHolderName")
    private String accountHolderName;

    @JsonProperty("iban")
    private String iban;

    @JsonProperty("signatureDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signatureDate;

    @JsonProperty("status")
    private MandateStatus status;

}
