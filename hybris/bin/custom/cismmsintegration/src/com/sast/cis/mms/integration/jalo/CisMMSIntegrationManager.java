package com.sast.cis.mms.integration.jalo;

import com.sast.cis.mms.integration.constants.CisSmmIntegrationConstants;
import de.hybris.platform.jalo.JaloSession;
import de.hybris.platform.jalo.extension.ExtensionManager;
import org.apache.log4j.Logger;

public class CisMMSIntegrationManager extends GeneratedCisMMSIntegrationManager
{
	@SuppressWarnings("unused")
	private static final Logger log = Logger.getLogger( CisMMSIntegrationManager.class.getName() );
	
	public static final CisMMSIntegrationManager getInstance()
	{
		ExtensionManager em = JaloSession.getCurrentSession().getExtensionManager();
		return (CisMMSIntegrationManager) em.getExtension(CisSmmIntegrationConstants.EXTENSIONNAME);
	}
	
}
