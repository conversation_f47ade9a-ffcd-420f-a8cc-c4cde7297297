package com.sast.cis.mms.integration.service;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.mms.integration.client.MMSGatewayApiServiceFactory;
import com.sast.cis.mms.integration.client.api.MandateStatus;
import com.sast.cis.mms.integration.client.api.SepaMandateApi;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MandateManagementServiceTest {

    @Mock
    private MMSGatewayApiServiceFactory MMSGatewayApiServiceFactory;

    @Mock
    private SepaMandateApi sepaMandateApi;

    @Mock
    private IoTCompanyModel company;

    @InjectMocks
    private MandateManagementService mandateManagementService;

    @Before
    public void setUp() {
        when(MMSGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(company.getUid()).thenReturn("test-company-uid");
    }

    @Test
    public void testCreateDraftMandateSuccess() {
        // Given
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-123")
                .companyId("test-company-uid")
                .status(MandateStatus.DRAFT)
                .build();
        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(apiDto, HttpStatus.OK);
        when(sepaMandateApi.createMandate("test-company-uid")).thenReturn(response);

        // When
        SepaMandateDto result = mandateManagementService.createDraftMandate(company);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-123", result.getMandateReference());
        assertEquals("Company ID should match", "test-company-uid", result.getCompanyId());
        assertEquals("Status should be DRAFT", MandateStatus.DRAFT, result.getStatus());
        verify(sepaMandateApi).createMandate("test-company-uid");
    }

    @Test
    public void testCreateDraftMandateSuccessWithCreatedStatus() {
        // Given
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-456")
                .companyId("test-company-uid")
                .status(MandateStatus.DRAFT)
                .build();
        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(apiDto, HttpStatus.CREATED);
        when(sepaMandateApi.createMandate("test-company-uid")).thenReturn(response);

        // When
        SepaMandateDto result = mandateManagementService.createDraftMandate(company);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-456", result.getMandateReference());
        assertEquals("Company ID should match", "test-company-uid", result.getCompanyId());
        assertEquals("Status should be DRAFT", MandateStatus.DRAFT, result.getStatus());
        verify(sepaMandateApi).createMandate("test-company-uid");
    }

    @Test
    public void testCreateDraftMandateFailureWithBadRequest() {
        // Given
        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        when(sepaMandateApi.createMandate("test-company-uid")).thenReturn(response);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(company);
        });

        assertTrue("Exception message should contain status",
                exception.getMessage().contains("Failed to create draft mandate. Status: 400 BAD_REQUEST"));
        verify(sepaMandateApi).createMandate("test-company-uid");
    }

    @Test
    public void testCreateDraftMandateFailureWithInternalServerError() {
        // Given
        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        when(sepaMandateApi.createMandate("test-company-uid")).thenReturn(response);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(company);
        });

        assertTrue("Exception message should contain status",
                exception.getMessage().contains("Failed to create draft mandate. Status: 500 INTERNAL_SERVER_ERROR"));
        verify(sepaMandateApi).createMandate("test-company-uid");
    }

    @Test
    public void testCreateDraftMandateApiException() {
        // Given
        when(sepaMandateApi.createMandate("test-company-uid")).thenThrow(new RuntimeException("API connection failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(company);
        });

        assertEquals("Exception message should match", "Failed to create draft mandate", exception.getMessage());
        assertNotNull("Exception should have a cause", exception.getCause());
        assertEquals("Cause message should match", "API connection failed", exception.getCause().getMessage());
        verify(sepaMandateApi).createMandate("test-company-uid");
    }

    @Test
    public void testFinalizeDraftMandateSuccess() {
        // Given
        String reference = "MANDATE-789";
        LocalDate signatureDate = LocalDate.of(2023, 12, 15);
        SepaMandateDto inputDto = SepaMandateDto.builder()
                .mandateReference(reference)
                .accountHolderName("John Doe")
                .iban("**********************")
                .signatureDate(signatureDate)
                .build();

        SepaMandateApiDto responseApiDto = SepaMandateApiDto.builder()
                .mandateReference(reference)
                .accountHolderName("John Doe")
                .iban("**********************")
                .signatureDate(signatureDate)
                .status(MandateStatus.ACTIVE)
                .build();

        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(responseApiDto, HttpStatus.OK);
        when(sepaMandateApi.activateMandate(eq(reference), any(SepaMandateApiDto.class))).thenReturn(response);

        // When
        SepaMandateDto result = mandateManagementService.finalizeDraftMandate(reference, inputDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", reference, result.getMandateReference());
        assertEquals("Account holder name should match", "John Doe", result.getAccountHolderName());
        assertEquals("IBAN should match", "**********************", result.getIban());
        assertEquals("Signature date should match", signatureDate, result.getSignatureDate());
        assertEquals("Status should be ACTIVE", MandateStatus.ACTIVE, result.getStatus());
        verify(sepaMandateApi).activateMandate(eq(reference), any(SepaMandateApiDto.class));
    }

    @Test
    public void testFinalizeDraftMandateWithNullInput() {
        // Given
        String reference = "MANDATE-NULL";
        SepaMandateDto inputDto = null;

        SepaMandateApiDto responseApiDto = SepaMandateApiDto.builder()
                .mandateReference(reference)
                .status(MandateStatus.ACTIVE)
                .build();

        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(responseApiDto, HttpStatus.OK);
        when(sepaMandateApi.activateMandate(eq(reference), any(SepaMandateApiDto.class))).thenReturn(response);

        // When
        SepaMandateDto result = mandateManagementService.finalizeDraftMandate(reference, inputDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", reference, result.getMandateReference());
        assertEquals("Status should be ACTIVE", MandateStatus.ACTIVE, result.getStatus());
        verify(sepaMandateApi).activateMandate(eq(reference), any(SepaMandateApiDto.class));
    }

    @Test
    public void testFinalizeDraftMandateFailureWithBadRequest() {
        // Given
        String reference = "MANDATE-FAIL";
        SepaMandateDto inputDto = SepaMandateDto.builder()
                .mandateReference(reference)
                .build();

        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        when(sepaMandateApi.activateMandate(eq(reference), any(SepaMandateApiDto.class))).thenReturn(response);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.finalizeDraftMandate(reference, inputDto);
        });

        assertTrue("Exception message should contain status",
                exception.getMessage().contains("Failed to finalize mandate. Status: 400 BAD_REQUEST"));
        verify(sepaMandateApi).activateMandate(eq(reference), any(SepaMandateApiDto.class));
    }

    @Test
    public void testFinalizeDraftMandateFailureWithNotFound() {
        // Given
        String reference = "MANDATE-NOT-FOUND";
        SepaMandateDto inputDto = SepaMandateDto.builder()
                .mandateReference(reference)
                .build();

        ResponseEntity<SepaMandateApiDto> response = new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        when(sepaMandateApi.activateMandate(eq(reference), any(SepaMandateApiDto.class))).thenReturn(response);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.finalizeDraftMandate(reference, inputDto);
        });

        assertTrue("Exception message should contain status",
                exception.getMessage().contains("Failed to finalize mandate. Status: 404 NOT_FOUND"));
        verify(sepaMandateApi).activateMandate(eq(reference), any(SepaMandateApiDto.class));
    }

    @Test
    public void testFinalizeDraftMandateApiException() {
        // Given
        String reference = "MANDATE-EXCEPTION";
        SepaMandateDto inputDto = SepaMandateDto.builder()
                .mandateReference(reference)
                .build();

        when(sepaMandateApi.activateMandate(eq(reference), any(SepaMandateApiDto.class)))
                .thenThrow(new RuntimeException("Network timeout"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.finalizeDraftMandate(reference, inputDto);
        });

        assertEquals("Exception message should match", "Failed to finalize mandate", exception.getMessage());
        assertNotNull("Exception should have a cause", exception.getCause());
        assertEquals("Cause message should match", "Network timeout", exception.getCause().getMessage());
        verify(sepaMandateApi).activateMandate(eq(reference), any(SepaMandateApiDto.class));
    }
}
