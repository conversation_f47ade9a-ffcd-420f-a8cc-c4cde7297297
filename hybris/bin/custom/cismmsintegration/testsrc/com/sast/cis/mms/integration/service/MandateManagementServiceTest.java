package com.sast.cis.mms.integration.service;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.mms.integration.client.MMSGatewayApiServiceFactory;
import com.sast.cis.mms.integration.client.api.SepaMandateApi;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import com.sast.cis.mms.integration.tools.MMSConverter;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@UnitTest
public class MandateManagementServiceTest {

    @Mock
    private MMSGatewayApiServiceFactory mmsGatewayApiServiceFactory;

    @Mock
    private SepaMandateApi sepaMandateApi;

    @InjectMocks
    private MandateManagementService mandateManagementService;

    private IoTCompanyModel testCompany;
    private SepaMandateApiDto testApiDto;
    private SepaMandateDto testMandateDto;

    @Before
    public void setUp() {
        testCompany = new IoTCompanyModel();
        testCompany.setUid("company-123");

        testApiDto = new SepaMandateApiDto();
        // Set up test API DTO properties as needed

        testMandateDto = new SepaMandateDto();
        testMandateDto.setMandateReference("mandate-ref-123");
        // Set up test mandate DTO properties as needed
    }

    @Test
    public void createDraftMandate_Success_WithOkStatus() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(testCompany.getUid()))
                .thenReturn(new ResponseEntity<>(testApiDto, HttpStatus.OK));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toDto(testApiDto))
                    .thenReturn(testMandateDto);

            // Act
            SepaMandateDto result = mandateManagementService.createDraftMandate(testCompany);

            // Assert
            assertNotNull(result);
            assertEquals(testMandateDto.getMandateReference(), result.getMandateReference());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).createMandate(testCompany.getUid());
            mockedConverter.verify(() -> MMSConverter.toDto(testApiDto));
        }
    }

    @Test
    public void createDraftMandate_Success_WithCreatedStatus() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(testCompany.getUid()))
                .thenReturn(new ResponseEntity<>(testApiDto, HttpStatus.CREATED));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toDto(testApiDto))
                    .thenReturn(testMandateDto);

            // Act
            SepaMandateDto result = mandateManagementService.createDraftMandate(testCompany);

            // Assert
            assertNotNull(result);
            assertEquals(testMandateDto.getMandateReference(), result.getMandateReference());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).createMandate(testCompany.getUid());
            mockedConverter.verify(() -> MMSConverter.toDto(testApiDto));
        }
    }

    @Test
    public void createDraftMandate_Failure_BadRequestStatus() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(testCompany.getUid()))
                .thenReturn(new ResponseEntity<>(null, HttpStatus.BAD_REQUEST));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(testCompany);
        });

        assertTrue(exception.getMessage().contains("Failed to create draft mandate"));
        assertTrue(exception.getMessage().contains("400 BAD_REQUEST"));
        verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
        verify(sepaMandateApi).createMandate(testCompany.getUid());
    }

    @Test
    public void createDraftMandate_Failure_ApiThrowsException() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(testCompany.getUid()))
                .thenThrow(new RuntimeException("API communication error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(testCompany);
        });

        assertEquals("Failed to create draft mandate", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("API communication error", exception.getCause().getMessage());
        verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
        verify(sepaMandateApi).createMandate(testCompany.getUid());
    }

    @Test
    public void createDraftMandate_Failure_FactoryThrowsException() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi())
                .thenThrow(new RuntimeException("Factory initialization error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(testCompany);
        });

        assertEquals("Failed to create draft mandate", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("Factory initialization error", exception.getCause().getMessage());
        verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
    }

    @Test
    public void finalizeDraftMandate_Success() {
        // Arrange
        String reference = "mandate-ref-123";
        SepaMandateApiDto finalizedApiDto = new SepaMandateApiDto();
        SepaMandateDto finalizedMandateDto = new SepaMandateDto();
        finalizedMandateDto.setMandateReference(reference);

        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.activateMandate(reference, testApiDto))
                .thenReturn(new ResponseEntity<>(finalizedApiDto, HttpStatus.OK));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenReturn(testApiDto);
            mockedConverter.when(() -> MMSConverter.toDto(finalizedApiDto))
                    .thenReturn(finalizedMandateDto);

            // Act
            SepaMandateDto result = mandateManagementService.finalizeDraftMandate(reference, testMandateDto);

            // Assert
            assertNotNull(result);
            assertEquals(reference, result.getMandateReference());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).activateMandate(reference, testApiDto);
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
            mockedConverter.verify(() -> MMSConverter.toDto(finalizedApiDto));
        }
    }

    @Test
    public void finalizeDraftMandate_Failure_BadRequestStatus() {
        // Arrange
        String reference = "mandate-ref-123";
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.activateMandate(reference, testApiDto))
                .thenReturn(new ResponseEntity<>(null, HttpStatus.BAD_REQUEST));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenReturn(testApiDto);

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                mandateManagementService.finalizeDraftMandate(reference, testMandateDto);
            });

            assertTrue(exception.getMessage().contains("Failed to finalize mandate"));
            assertTrue(exception.getMessage().contains("400 BAD_REQUEST"));
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).activateMandate(reference, testApiDto);
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
        }
    }

    @Test
    public void finalizeDraftMandate_Failure_ApiThrowsException() {
        // Arrange
        String reference = "mandate-ref-123";
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.activateMandate(reference, testApiDto))
                .thenThrow(new RuntimeException("API communication error"));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenReturn(testApiDto);

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                mandateManagementService.finalizeDraftMandate(reference, testMandateDto);
            });

            assertEquals("Failed to finalize mandate", exception.getMessage());
            assertNotNull(exception.getCause());
            assertEquals("API communication error", exception.getCause().getMessage());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).activateMandate(reference, testApiDto);
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
        }
    }

    @Test
    public void finalizeDraftMandate_Failure_ConverterThrowsException() {
        // Arrange
        String reference = "mandate-ref-123";
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenThrow(new RuntimeException("Conversion error"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                mandateManagementService.finalizeDraftMandate(reference, testMandateDto);
            });

            assertEquals("Failed to finalize mandate", exception.getMessage());
            assertNotNull(exception.getCause());
            assertEquals("Conversion error", exception.getCause().getMessage());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verifyNoInteractions(sepaMandateApi);
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
        }
    }

    @Test
    public void createDraftMandate_NullCompanyUid() {
        // Arrange
        IoTCompanyModel companyWithNullUid = new IoTCompanyModel();
        companyWithNullUid.setUid(null);

        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(null))
                .thenThrow(new IllegalArgumentException("Company UID cannot be null"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mandateManagementService.createDraftMandate(companyWithNullUid);
        });

        assertEquals("Failed to create draft mandate", exception.getMessage());
        assertNotNull(exception.getCause());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
        verify(sepaMandateApi).createMandate(null);
    }

    @Test
    public void finalizeDraftMandate_NullReference() {
        // Arrange
        String nullReference = null;

        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.activateMandate(eq(nullReference), any(SepaMandateApiDto.class)))
                .thenThrow(new IllegalArgumentException("Reference cannot be null"));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenReturn(testApiDto);

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                mandateManagementService.finalizeDraftMandate(nullReference, testMandateDto);
            });

            assertEquals("Failed to finalize mandate", exception.getMessage());
            assertNotNull(exception.getCause());
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).activateMandate(eq(nullReference), any(SepaMandateApiDto.class));
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
        }
    }

    @Test
    public void createDraftMandate_ResponseBodyIsNull() {
        // Arrange
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.createMandate(testCompany.getUid()))
                .thenReturn(new ResponseEntity<>(null, HttpStatus.OK));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toDto(null))
                    .thenReturn(testMandateDto);

            // Act
            SepaMandateDto result = mandateManagementService.createDraftMandate(testCompany);

            // Assert
            assertNotNull(result);
            assertEquals(testMandateDto.getMandateReference(), result.getMandateReference());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).createMandate(testCompany.getUid());
            mockedConverter.verify(() -> MMSConverter.toDto(null));
        }
    }

    @Test
    public void finalizeDraftMandate_ResponseBodyIsNull() {
        // Arrange
        String reference = "mandate-ref-123";
        when(mmsGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.activateMandate(reference, testApiDto))
                .thenReturn(new ResponseEntity<>(null, HttpStatus.OK));

        try (MockedStatic<MMSConverter> mockedConverter = mockStatic(MMSConverter.class)) {
            mockedConverter.when(() -> MMSConverter.toApiDto(testMandateDto))
                    .thenReturn(testApiDto);
            mockedConverter.when(() -> MMSConverter.toDto(null))
                    .thenReturn(testMandateDto);

            // Act
            SepaMandateDto result = mandateManagementService.finalizeDraftMandate(reference, testMandateDto);

            // Assert
            assertNotNull(result);
            assertEquals(testMandateDto.getMandateReference(), result.getMandateReference());
            verify(mmsGatewayApiServiceFactory).getSepaMandateApi();
            verify(sepaMandateApi).activateMandate(reference, testApiDto);
            mockedConverter.verify(() -> MMSConverter.toApiDto(testMandateDto));
            mockedConverter.verify(() -> MMSConverter.toDto(null));
        }
    }
}