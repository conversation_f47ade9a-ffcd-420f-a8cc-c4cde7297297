package com.sast.cis.mms.integration.tools;

import com.sast.cis.mms.integration.client.api.MandateStatus;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;

import java.time.LocalDate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@UnitTest
public class MMSConverterTest {

    @Test
    public void testToDto() {
        // Test with null input
        assertNull("Result should be null when input is null", MMSConverter.toDto(null));

        // Test with complete data
        LocalDate signatureDate = LocalDate.of(2023, 12, 15);
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-123")
                .companyId("COMPANY-456")
                .accountHolderName("<PERSON> Doe")
                .iban("**********************")
                .signatureDate(signatureDate)
                .status(MandateStatus.ACTIVE)
                .build();

        SepaMandateDto result = MMSConverter.toDto(apiDto);

        assertNotNull(result);
        assertEquals("MANDATE-123", result.getMandateReference());
        assertEquals("COMPANY-456", result.getCompanyId());
        assertEquals("John Doe", result.getAccountHolderName());
        assertEquals("**********************", result.getIban());
        assertEquals(signatureDate, result.getSignatureDate());
        assertEquals(MandateStatus.ACTIVE, result.getStatus());
    }

    @Test
    public void testToApiDto() {
        // Test with null input
        assertNull("Result should be null when input is null", MMSConverter.toApiDto(null));

        // Test with complete data
        LocalDate signatureDate = LocalDate.of(2023, 11, 20);
        SepaMandateDto dto = SepaMandateDto.builder()
                .mandateReference("MANDATE-999")
                .companyId("COMPANY-888")
                .accountHolderName("Jane Smith")
                .iban("***************************")
                .signatureDate(signatureDate)
                .status(MandateStatus.ACTIVE)
                .build();

        SepaMandateApiDto result = MMSConverter.toApiDto(dto);

        assertNotNull(result);
        assertEquals("MANDATE-999", result.getMandateReference());
        assertEquals("COMPANY-888", result.getCompanyId());
        assertEquals("Jane Smith", result.getAccountHolderName());
        assertEquals("***************************", result.getIban());
        assertEquals(signatureDate, result.getSignatureDate());
        assertEquals(MandateStatus.ACTIVE, result.getStatus());
    }
}
