package com.sast.cis.mms.integration.tools;

import com.sast.cis.mms.integration.client.api.MandateStatus;
import com.sast.cis.mms.integration.client.api.SepaMandateApiDto;
import com.sast.cis.mms.integration.dto.SepaMandateDto;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;

import java.time.LocalDate;

import static org.junit.Assert.*;

//@UnitTest
public class MMSConverterTest {

    @Test
    public void testToDtoWithNullInput() {
        // Given
        SepaMandateApiDto apiDto = null;

        // When
        SepaMandateDto result = MMSConverter.toDto(apiDto);

        // Then
        assertNull("Result should be null when input is null", result);
    }

    @Test
    public void testToDtoWithCompleteApiDto() {
        // Given
        LocalDate signatureDate = LocalDate.of(2023, 12, 15);
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-123")
                .companyId("COMPANY-456")
                .accountHolderName("John Doe")
                .iban("**********************")
                .signatureDate(signatureDate)
                .status(MandateStatus.ACTIVE)
                .build();

        // When
        SepaMandateDto result = MMSConverter.toDto(apiDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-123", result.getMandateReference());
        assertEquals("Company ID should match", "COMPANY-456", result.getCompanyId());
        assertEquals("Account holder name should match", "John Doe", result.getAccountHolderName());
        assertEquals("IBAN should match", "**********************", result.getIban());
        assertEquals("Signature date should match", signatureDate, result.getSignatureDate());
        assertEquals("Status should match", MandateStatus.ACTIVE, result.getStatus());
    }

    @Test
    public void testToDtoWithPartialApiDto() {
        // Given
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-789")
                .status(MandateStatus.DRAFT)
                .build();

        // When
        SepaMandateDto result = MMSConverter.toDto(apiDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-789", result.getMandateReference());
        assertEquals("Status should match", MandateStatus.DRAFT, result.getStatus());
        assertNull("Company ID should be null", result.getCompanyId());
        assertNull("Account holder name should be null", result.getAccountHolderName());
        assertNull("IBAN should be null", result.getIban());
        assertNull("Signature date should be null", result.getSignatureDate());
    }

    @Test
    public void testToDtoWithEmptyApiDto() {
        // Given
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder().build();

        // When
        SepaMandateDto result = MMSConverter.toDto(apiDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertNull("Mandate reference should be null", result.getMandateReference());
        assertNull("Company ID should be null", result.getCompanyId());
        assertNull("Account holder name should be null", result.getAccountHolderName());
        assertNull("IBAN should be null", result.getIban());
        assertNull("Signature date should be null", result.getSignatureDate());
        assertNull("Status should be null", result.getStatus());
    }

    @Test
    public void testToDtoWithAllMandateStatuses() {
        // Test DRAFT status
        SepaMandateApiDto draftApiDto = SepaMandateApiDto.builder()
                .mandateReference("DRAFT-123")
                .status(MandateStatus.DRAFT)
                .build();
        SepaMandateDto draftResult = MMSConverter.toDto(draftApiDto);
        assertEquals("DRAFT status should be converted correctly", MandateStatus.DRAFT, draftResult.getStatus());

        // Test ACTIVE status
        SepaMandateApiDto activeApiDto = SepaMandateApiDto.builder()
                .mandateReference("ACTIVE-123")
                .status(MandateStatus.ACTIVE)
                .build();
        SepaMandateDto activeResult = MMSConverter.toDto(activeApiDto);
        assertEquals("ACTIVE status should be converted correctly", MandateStatus.ACTIVE, activeResult.getStatus());

        // Test DELETED status
        SepaMandateApiDto deletedApiDto = SepaMandateApiDto.builder()
                .mandateReference("DELETED-123")
                .status(MandateStatus.DELETED)
                .build();
        SepaMandateDto deletedResult = MMSConverter.toDto(deletedApiDto);
        assertEquals("DELETED status should be converted correctly", MandateStatus.DELETED, deletedResult.getStatus());
    }

    @Test
    public void testToApiDtoWithNullInput() {
        // Given
        SepaMandateDto dto = null;

        // When
        SepaMandateApiDto result = MMSConverter.toApiDto(dto);

        // Then
        assertNull("Result should be null when input is null", result);
    }

    @Test
    public void testToApiDtoWithCompleteDto() {
        // Given
        LocalDate signatureDate = LocalDate.of(2023, 11, 20);
        SepaMandateDto dto = SepaMandateDto.builder()
                .mandateReference("MANDATE-999")
                .companyId("COMPANY-888")
                .accountHolderName("Jane Smith")
                .iban("***************************")
                .signatureDate(signatureDate)
                .status(MandateStatus.ACTIVE)
                .build();

        // When
        SepaMandateApiDto result = MMSConverter.toApiDto(dto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-999", result.getMandateReference());
        assertEquals("Company ID should match", "COMPANY-888", result.getCompanyId());
        assertEquals("Account holder name should match", "Jane Smith", result.getAccountHolderName());
        assertEquals("IBAN should match", "***************************", result.getIban());
        assertEquals("Signature date should match", signatureDate, result.getSignatureDate());
        assertEquals("Status should match", MandateStatus.ACTIVE, result.getStatus());
    }

    @Test
    public void testToApiDtoWithPartialDto() {
        // Given
        SepaMandateDto dto = SepaMandateDto.builder()
                .mandateReference("MANDATE-111")
                .accountHolderName("Bob Johnson")
                .build();

        // When
        SepaMandateApiDto result = MMSConverter.toApiDto(dto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference should match", "MANDATE-111", result.getMandateReference());
        assertEquals("Account holder name should match", "Bob Johnson", result.getAccountHolderName());
        assertNull("Company ID should be null", result.getCompanyId());
        assertNull("IBAN should be null", result.getIban());
        assertNull("Signature date should be null", result.getSignatureDate());
        assertNull("Status should be null", result.getStatus());
    }

    @Test
    public void testToApiDtoWithEmptyDto() {
        // Given
        SepaMandateDto dto = SepaMandateDto.builder().build();

        // When
        SepaMandateApiDto result = MMSConverter.toApiDto(dto);

        // Then
        assertNotNull("Result should not be null", result);
        assertNull("Mandate reference should be null", result.getMandateReference());
        assertNull("Company ID should be null", result.getCompanyId());
        assertNull("Account holder name should be null", result.getAccountHolderName());
        assertNull("IBAN should be null", result.getIban());
        assertNull("Signature date should be null", result.getSignatureDate());
        assertNull("Status should be null", result.getStatus());
    }

    @Test
    public void testRoundTripConversion() {
        // Given
        LocalDate signatureDate = LocalDate.of(2023, 10, 5);
        SepaMandateApiDto originalApiDto = SepaMandateApiDto.builder()
                .mandateReference("ROUND-TRIP-123")
                .companyId("COMPANY-ROUND")
                .accountHolderName("Round Trip User")
                .iban("**********************")
                .signatureDate(signatureDate)
                .status(MandateStatus.DRAFT)
                .build();

        // When
        SepaMandateDto dto = MMSConverter.toDto(originalApiDto);
        SepaMandateApiDto resultApiDto = MMSConverter.toApiDto(dto);

        // Then
        assertNotNull("DTO should not be null", dto);
        assertNotNull("Result API DTO should not be null", resultApiDto);
        assertEquals("Mandate reference should be preserved", originalApiDto.getMandateReference(), resultApiDto.getMandateReference());
        assertEquals("Company ID should be preserved", originalApiDto.getCompanyId(), resultApiDto.getCompanyId());
        assertEquals("Account holder name should be preserved", originalApiDto.getAccountHolderName(), resultApiDto.getAccountHolderName());
        assertEquals("IBAN should be preserved", originalApiDto.getIban(), resultApiDto.getIban());
        assertEquals("Signature date should be preserved", originalApiDto.getSignatureDate(), resultApiDto.getSignatureDate());
        assertEquals("Status should be preserved", originalApiDto.getStatus(), resultApiDto.getStatus());
    }

    @Test
    public void testConversionWithSpecialCharacters() {
        // Given
        SepaMandateApiDto apiDto = SepaMandateApiDto.builder()
                .mandateReference("MANDATE-ÄÖÜ-123")
                .companyId("COMPANY-ßÇ")
                .accountHolderName("Müller & Söhne GmbH")
                .iban("********************")
                .status(MandateStatus.ACTIVE)
                .build();

        // When
        SepaMandateDto result = MMSConverter.toDto(apiDto);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Mandate reference with special chars should match", "MANDATE-ÄÖÜ-123", result.getMandateReference());
        assertEquals("Company ID with special chars should match", "COMPANY-ßÇ", result.getCompanyId());
        assertEquals("Account holder name with special chars should match", "Müller & Söhne GmbH", result.getAccountHolderName());
        assertEquals("IBAN should match", "********************", result.getIban());
        assertEquals("Status should match", MandateStatus.ACTIVE, result.getStatus());
    }
}
