package com.sast.cis.mms.integration.client.authentication

import com.sast.cis.mms.integration.client.authentication.config.MMSGatewayConfigProvider
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import org.junit.Test
import org.springframework.context.annotation.Import
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder

import javax.annotation.Resource

@IntegrationTest
@Import([MMSAccessTokenProvider.class, MMSGatewayConfigProvider.class])
class MMSAccessTokenProviderITest extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private MMSAccessTokenProvider mmsAccessTokenProvider

    @Resource
    private ConfigurationService configurationService

    private JwtDecoder jwtDecoder

    private String issuer

    def setup() {
//        issuer = configurationService.getConfiguration().getString("sepa.mandate.auth.idp.url")
//        jwtDecoder = NimbusJwtDecoder.withJwkSetUri("%s/protocol/openid-connect/certs".formatted(issuer)).build()
    }

    @Test
    def "debug available beans"() {
        when:
        def beanNames = applicationContext.getBeanDefinitionNames()
        // print out each from the new line
        println "All Available beans: "
        beanNames.each { println it }

        then:
        true
    }



//    @Test
//    def "should return a valid access token"()
//
//    {
//        when:
//        def accessToken = mMSAccessTokenProvider.getAccessToken()
//
//        then:
//        accessToken
//        def decodedToken = jwtDecoder.decode(accessToken)
//        decodedToken.issuer.toString() == issuer
//    }

}