package com.sast.cis.payment.adyen.service.directdebit.transaction;

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.service.AbstractOrderHashService;
import com.sast.cis.payment.adyen.util.AdyenCodeGenerator;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.time.TimeService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

import static com.google.common.base.Preconditions.checkState;
import static com.sast.cis.core.enums.PaymentProvider.ADYEN;
import static com.sast.cis.core.paymentintegration.paymentinfo.PaymentInfoPreconditions.hasInfoType;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Factory for creating Adyen payment transactions and entries.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AdyenTransactionFactory {

    private final ModelService modelService;
    private final TimeService timeService;
    private final AbstractOrderHashService abstractOrderHashService;

    /**
     * Creates a new Adyen payment transaction of the given type for the given order and with the given amount.
     * <br/>
     * Note: The given order is refreshed after creation.
     *
     * @param paymentTransactionType type of the newly created transaction
     * @param order                  order for which the transaction is created
     * @param amount                 planned amount for this transaction
     * @return a new payment transaction
     * @throws IllegalStateException if the order has no payment info
     * @throws IllegalStateException if the payment info of the given order is not for payment provider Adyen
     * @throws IllegalStateException if the order payment info has no merchant ID (blank strings are considered as such as well)
     */
    public PaymentTransactionModel createTransaction(
        @NonNull final PaymentTransactionType paymentTransactionType,
        @NonNull final AbstractOrderModel order,
        @NonNull final BigDecimal amount) {

        final PaymentInfoModel orderPaymentInfo = validateAndGetOrderInfo(order);

        final PaymentTransactionModel paymentTransaction = initiateTransactionOfType(paymentTransactionType);
        paymentTransaction.setCurrency(order.getCurrency());
        paymentTransaction.setPlannedAmount(amount);
        paymentTransaction.setOrder(order);
        paymentTransaction.setInfo(orderPaymentInfo);
        paymentTransaction.setPgwMerchantId(orderPaymentInfo.getPgwMerchantId());

        modelService.save(paymentTransaction);
        modelService.refresh(order);

        return paymentTransaction;
    }

    /**
     * Creates a new Adyen payment transaction of the given type for the given payment info.
     * <br/>
     * This method is used when the transaction is not directly associated with an order, but rather with a payment info.
     * This is typically the case for tokenization or other payment-related operations that do not involve a specific order.
     * <br/>
     * Note: The given order is refreshed after creation.
     *
     * @param paymentTransactionType type of the newly created transaction
     * @param paymentInfo            order for which the transaction is created
     * @return a new payment transaction
     * @throws IllegalStateException if the payment info of the given order is not for payment provider Adyen
     * @throws IllegalStateException if the order payment info has no merchant ID (blank strings are considered as such as well)
     */
    public PaymentTransactionModel createTransaction(
        @NonNull final PaymentTransactionType paymentTransactionType,
        @NonNull final PaymentInfoModel paymentInfo) {

        final PaymentTransactionModel paymentTransaction = initiateTransactionOfType(paymentTransactionType);
        paymentTransaction.setInfo(paymentInfo);
        paymentTransaction.setPgwMerchantId(paymentInfo.getPgwMerchantId());

        modelService.save(paymentTransaction);
        modelService.refresh(paymentInfo);

        return paymentTransaction;
    }

    /**
     * Creates a new payment transaction entry for the given transaction.
     *
     * @param paymentTransaction          the payment transaction for which the entry is created
     * @param paymentTransactionEntryType type of the newly created entry
     * @param transactionStatus           status of the transaction
     * @return a new payment transaction entry
     * @throws IllegalStateException if the given payment transaction has no order
     */
    public PaymentTransactionEntryModel createEntry(
        @NonNull final PaymentTransactionModel paymentTransaction,
        @NonNull final PaymentTransactionType paymentTransactionEntryType,
        @NonNull final TransactionStatus transactionStatus) {

        final PaymentTransactionEntryModel paymentTransactionEntry = modelService.create(PaymentTransactionEntryModel.class);
        paymentTransactionEntry.setCode(AdyenCodeGenerator.forPaymentTransactionEntry());
        paymentTransactionEntry.setPaymentTransaction(paymentTransaction);
        paymentTransactionEntry.setTransactionStatus(transactionStatus.name());
        paymentTransactionEntry.setType(paymentTransactionEntryType);
        paymentTransactionEntry.setCurrency(paymentTransaction.getCurrency());
        paymentTransactionEntry.setTime(timeService.getCurrentTime());

        ofNullable(paymentTransaction.getOrder()).ifPresent(order -> {
            paymentTransactionEntry.setCartHash(abstractOrderHashService.calculateHash(order));
        });

        modelService.save(paymentTransactionEntry);
        modelService.refresh(paymentTransaction);

        LOG.info("Created new payment transaction entry {}", paymentTransactionEntry.getCode());
        return paymentTransactionEntry;
    }

    private PaymentTransactionModel initiateTransactionOfType(final PaymentTransactionType paymentTransactionType) {
        final PaymentTransactionModel transaction = modelService.create(PaymentTransactionModel.class);
        transaction.setCode(AdyenCodeGenerator.forPaymentTransaction());
        transaction.setType(paymentTransactionType);
        transaction.setPaymentProvider(ADYEN.getCode());
        return transaction;
    }

    private PaymentInfoModel validateAndGetOrderInfo(final AbstractOrderModel order) {
        final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = hasInfoType(order, TokenizedSepaDirectDebitPaymentInfoModel.class);
        checkState(paymentInfo != null,
            "Given order %s has no payment info", order.getCode());
        checkState(paymentInfo.getPaymentProvider() == ADYEN,
            "Payment info %s for given order %s is not for Adyen", paymentInfo.getCode(), order.getCode());
        checkState(isNotBlank(paymentInfo.getPgwMerchantId()),
            "Payment info %s for given order %s has no merchantId", paymentInfo.getCode(), order.getCode());
        return paymentInfo;
    }
}
