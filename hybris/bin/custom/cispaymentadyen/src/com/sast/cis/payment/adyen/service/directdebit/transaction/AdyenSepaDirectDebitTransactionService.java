package com.sast.cis.payment.adyen.service.directdebit.transaction;

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.payment.adyen.client.tokenization.TokenizationResult;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.dto.TransactionStatusDetails;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.google.common.base.Preconditions.checkState;
import static com.sast.cis.core.paymentintegration.paymentinfo.PaymentInfoPreconditions.hasInfoType;
import static de.hybris.platform.payment.dto.TransactionStatus.ACCEPTED;
import static de.hybris.platform.payment.dto.TransactionStatus.ERROR;
import static de.hybris.platform.payment.dto.TransactionStatus.PENDING;
import static de.hybris.platform.payment.dto.TransactionStatusDetails.SUCCESFULL;
import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION;
import static de.hybris.platform.payment.enums.PaymentTransactionType.TOKENIZATION;
import static java.math.BigDecimal.ZERO;

@Component
@Slf4j
@RequiredArgsConstructor
public class AdyenSepaDirectDebitTransactionService {

    private final AdyenTransactionFactory adyenTransactionFactory;
    private final ModelService modelService;

    /**
     * Initiates an authorization transaction for the given order.
     * This method creates a new payment transaction of type AUTHORIZATION, and attaches the paymentInfo of the current order to it.
     * It also creates a transaction entry of type AUTHORIZATION with status PENDING.
     *
     * @param order Order for which the authorization is created
     * @return The auth payment transaction
     * @throws IllegalStateException if the given order does not have a paymentInfo of type TokenizedSepaDirectDebitPaymentInfo
     */
    public PaymentTransactionModel initiateAuthorization(@NonNull final AbstractOrderModel order) {
        LOG.info("Initiate authorization transaction for order '{}'", order.getCode());
        hasInfoType(order, TokenizedSepaDirectDebitPaymentInfoModel.class);

        final PaymentTransactionModel transaction = adyenTransactionFactory.createTransaction(AUTHORIZATION, order, ZERO);
        final PaymentTransactionEntryModel entry = adyenTransactionFactory.createEntry(transaction, AUTHORIZATION, PENDING);

        LOG.info("Created authorization transaction '{}' with entry '{}'", transaction.getCode(), entry.getCode());
        return transaction;
    }

    /**
     * Initiates a tokenization transaction for the given payment info.
     * This method creates a new payment transaction of type TOKENIZATION, and attaches the given paymentInfo to it.
     * It also creates a transaction entry of type TOKENIZATION with status PENDING.
     *
     * @param paymentInfo - the payment info for which the tokenization is created
     * @return The tokenization payment transaction
     */
    public PaymentTransactionModel initiateTokenizationTransaction(@NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        LOG.info("Initiate tokenization transaction for payment info '{}'", paymentInfo.getCode());

        final PaymentTransactionModel transaction = adyenTransactionFactory.createTransaction(TOKENIZATION, paymentInfo);
        final PaymentTransactionEntryModel entry = adyenTransactionFactory.createEntry(transaction, TOKENIZATION, PENDING);

        LOG.info("Created tokenization transaction '{}' with entry '{}'", transaction.getCode(), entry.getCode());
        return transaction;
    }

    /**
     * Marks an authorization as successful
     * It does so by finding the PENDING transaction entry of type AUTHORIZATION for the given transaction, and updating its status to ACCEPTED.
     *
     * @param authTx entry to be marked as successful
     * @return The authorization success transaction entry
     * @throws IllegalStateException if the given transaction is not of type AUTHORIZATION
     * @throws IllegalStateException if no authorization entry could be found for the given transaction
     * @throws IllegalStateException if the transaction entry is not in status PENDING
     */
    public PaymentTransactionEntryModel saveAuthorizationSuccess(@NonNull final PaymentTransactionModel authTx) {
        checkState(authTx.getType() == AUTHORIZATION, "Given tx '%s' is not of type AUTHORIZATION", authTx.getCode());

        final PaymentTransactionEntryModel authEntry = findPendingEntryOfTypeOrThrow(authTx, AUTHORIZATION);
        setAccepted(authEntry);
        return authEntry;
    }

    /**
     * Marks a tokenization transaction as successful
     *
     * @param transaction the transaction to be marked as successful
     */
    public PaymentTransactionEntryModel saveTokenizationSuccess(
        @NonNull final PaymentTransactionModel transaction,
        @NonNull final TokenizationResult tokenizationResult) {

        checkState(transaction.getType() == TOKENIZATION, "Given tx '%s' is not of type TOKENIZATION", transaction.getCode());

        transaction.setRequestToken(tokenizationResult.pspReference());
        modelService.save(transaction);

        final PaymentTransactionEntryModel authEntry = findPendingEntryOfTypeOrThrow(transaction, TOKENIZATION);
        setAccepted(authEntry);
        return authEntry;
    }

    /**
     * Marks a tokenization transaction as failed
     *
     * @param transaction    the transaction to be marked as failed
     * @param failureDetails the details of the failure
     */
    public PaymentTransactionEntryModel saveTokenizationFailure(@NonNull final PaymentTransactionModel transaction,
        final TransactionStatusDetails failureDetails) {
        checkState(transaction.getType() == TOKENIZATION, "Given tx '%s' is not of type TOKENIZATION", transaction.getCode());

        final PaymentTransactionEntryModel authEntry = findPendingEntryOfTypeOrThrow(transaction, TOKENIZATION);
        setFailure(authEntry, failureDetails);
        return authEntry;
    }

    private void setAccepted(final PaymentTransactionEntryModel paymentTransactionEntry) {
        setStatus(paymentTransactionEntry, ACCEPTED, SUCCESFULL);
    }

    private void setFailure(
        final PaymentTransactionEntryModel paymentTransactionEntry,
        final TransactionStatusDetails transactionStatusDetails) {

        setStatus(paymentTransactionEntry, ERROR, transactionStatusDetails);
    }

    private void setStatus(
        final PaymentTransactionEntryModel paymentTransactionEntry,
        final TransactionStatus transactionStatus,
        final TransactionStatusDetails transactionStatusDetails) {

        paymentTransactionEntry.setTransactionStatus(transactionStatus.name());
        if (transactionStatusDetails != null) {
            paymentTransactionEntry.setTransactionStatusDetails(transactionStatusDetails.name());
        }
        modelService.save(paymentTransactionEntry);
        modelService.refresh(paymentTransactionEntry.getPaymentTransaction());

        LOG.info(
            "Updated status of PaymentTransactionEntry '{}' to '{}' with status detail '{}'",
            paymentTransactionEntry.getCode(), transactionStatus, transactionStatusDetails
        );
    }

    private PaymentTransactionEntryModel findPendingEntryOfTypeOrThrow(
        final PaymentTransactionModel transaction,
        final PaymentTransactionType transactionType) {

        final List<PaymentTransactionEntryModel> authEntries = transaction.getEntries()
            .stream()
            .filter(entry -> transactionType.equals(entry.getType()))
            .toList();

        if (authEntries.size() > 1) {
            throw new IllegalStateException(
                "Found more than one transaction entry of type '%s' for transaction '%s'".formatted(transactionType, transaction.getCode())
            );
        }

        final PaymentTransactionEntryModel txEntry = authEntries.stream()
            .findFirst()
            .orElseThrow(
                () -> new IllegalStateException(
                    "No transaction entry of type '%s' found for transaction '%s'".formatted(transactionType, transaction.getCode())
                )
            );

        if (!PENDING.name().equals(txEntry.getTransactionStatus())) {
            throw new IllegalStateException(
                "Expected transaction entry '%s' to be in status PENDING, but was '%s' instead."
                    .formatted(txEntry.getCode(), txEntry.getTransactionStatus())
            );
        }

        return txEntry;
    }
}
