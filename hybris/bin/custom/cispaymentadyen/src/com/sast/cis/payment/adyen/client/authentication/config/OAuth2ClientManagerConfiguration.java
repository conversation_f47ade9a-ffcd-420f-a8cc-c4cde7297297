package com.sast.cis.payment.adyen.client.authentication.config;

import com.sast.cis.payment.adyen.client.authentication.config.AdyenGatewayConfig.AuthenticationConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;

import static org.springframework.security.oauth2.core.AuthorizationGrantType.CLIENT_CREDENTIALS;

@Configuration
@RequiredArgsConstructor
public class OAuth2ClientManagerConfiguration {

    private final AdyenGatewayConfigProvider adyenGatewayConfigProvider;

    @Bean
    public ClientRegistration clientRegistration() {
        final AuthenticationConfig authenticationConfig = adyenGatewayConfigProvider.getConfig().authenticationConfig();
        return ClientRegistration
                .withRegistrationId(authenticationConfig.clientRegistrationId())
                .tokenUri(authenticationConfig.tokenEndpointUrl())
                .clientId(authenticationConfig.clientId())
                .clientSecret(authenticationConfig.clientSecret())
                .authorizationGrantType(CLIENT_CREDENTIALS)
                .build();
    }

    @Bean
    public ClientRegistrationRepository clientRegistrationRepository(final ClientRegistration clientRegistration) {
        return new InMemoryClientRegistrationRepository(clientRegistration);
    }

    @Bean
    public OAuth2AuthorizedClientService authorizedClientService(final ClientRegistrationRepository clientRegistrationRepository) {
        return new InMemoryOAuth2AuthorizedClientService(clientRegistrationRepository);
    }

    @Bean
    public AuthorizedClientServiceOAuth2AuthorizedClientManager oAuth2AuthorizedClientManager(
            final ClientRegistrationRepository clientRegistrationRepository,
            final OAuth2AuthorizedClientService authorizedClientService) {

        return new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, authorizedClientService);
    }
}
