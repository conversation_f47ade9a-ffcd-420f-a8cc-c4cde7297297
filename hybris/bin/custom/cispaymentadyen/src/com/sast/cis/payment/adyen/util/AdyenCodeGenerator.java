package com.sast.cis.payment.adyen.util;

import com.sast.cis.core.util.Base58UUIDCodeGenerator;

public class AdyenCodeGenerator {
    private static final String ACCOUNT_ID_PREFIX = "AdyenSellerAccount";
    private static final String TRANSACTION_PREFIX = "AdyenTx";
    private static final String TRANSACTION_ENTRY_PREFIX = "AdyenTxEntry";

    public static String forSellerAccount() {
        return Base58UUIDCodeGenerator.generateCode(ACCOUNT_ID_PREFIX);
    }

    public static String forPaymentTransaction() {
        return Base58UUIDCodeGenerator.generateCode(TRANSACTION_PREFIX);
    }

    public static String forPaymentTransactionEntry() {
        return Base58UUIDCodeGenerator.generateCode(TRANSACTION_ENTRY_PREFIX);
    }
}
