package com.sast.cis.payment.adyen.service;

import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.AuthorizationResult;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;

public interface AdyenPaymentMethodStrategy {

    PaymentMethodType getPaymentMethod();

    Class<? extends PaymentInfoModel> getPaymentInfoType();

    CheckoutInfo prepareCheckout(final AuthorizationParameter authorizationParameter);

    PaymentInfoModel createPaymentInfo(final IntegratorModel integrator, final PaymentInfoData paymentInfoData);

    void confirmSelectedPaymentInfo(final AuthorizationParameter authorizationParameter);

    PaymentTransactionEntryModel authorize(final AuthorizationParameter authorizationParameter);

    AuthorizationResult getAuthorizationResult(final PaymentTransactionEntryModel paymentTransactionEntry);
}
