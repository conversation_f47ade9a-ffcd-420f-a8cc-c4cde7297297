package com.sast.cis.payment.adyen.client.tokenization;

import de.hybris.platform.payment.model.PaymentTransactionModel;

public class TokenizationException extends RuntimeException {

    private TokenizationException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public static TokenizationException forTransaction(final PaymentTransactionModel tx, final Throwable cause) {
        return new TokenizationException(
            "Tokenization failed for transaction with code '%s'".formatted(tx.getCode()),
            cause
        );
    }
}
