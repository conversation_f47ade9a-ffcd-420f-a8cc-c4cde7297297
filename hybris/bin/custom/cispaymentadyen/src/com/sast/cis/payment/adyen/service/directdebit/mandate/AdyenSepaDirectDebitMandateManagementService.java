package com.sast.cis.payment.adyen.service.directdebit.mandate;

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.data.MandateDetails;
import com.sast.cis.payment.adyen.service.directdebit.AdyenTokenizedSepaDirectDebitPaymentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
@RequiredArgsConstructor
public class AdyenSepaDirectDebitMandateManagementService {

    private final AdyenTokenizedSepaDirectDebitPaymentInfoService paymentInfoService;

    public void activateMandateForPaymentInfo(final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        LOG.info("Activate mandate for payment info with code '{}'", paymentInfo.getCode());

        // Placeholder until the mms integration is implemented
        paymentInfoService.updateAfterMandateActivation(paymentInfo, toMandateDetails(paymentInfo));
    }

    private MandateDetails toMandateDetails(final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        return MandateDetails.of(
            paymentInfo.getMandateReference(),
            new Date()
        );
    }
}
