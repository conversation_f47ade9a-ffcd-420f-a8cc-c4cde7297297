package com.sast.cis.payment.adyen.service.directdebit;

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.paymentintegration.paymentinfo.PaymentInfoService;
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.TokenizedSepaDirectDebitPaymentInfoManagementService;
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.TokenizedSepaDirectDebitPaymentInfoRetrievalService;
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.data.MandateDetails;
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.data.TokenizationDetails;
import com.sast.cis.payment.adyen.client.tokenization.TokenizationResult;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Set;

import static com.sast.cis.core.enums.PaymentProvider.ADYEN;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdyenTokenizedSepaDirectDebitPaymentInfoService
    implements PaymentInfoService<TokenizedSepaDirectDebitPaymentInfoData, TokenizedSepaDirectDebitPaymentInfoModel> {

    private final TokenizedSepaDirectDebitPaymentInfoManagementService tokenizedPaymentInfoManagementService;
    private final TokenizedSepaDirectDebitPaymentInfoRetrievalService tokenizedPaymentInfoRetrievalService;

    public Set<TokenizedSepaDirectDebitPaymentInfoModel> getPaymentInfos(
        @NonNull final IntegratorModel integrator,
        @NonNull final String merchantId) {

        return tokenizedPaymentInfoRetrievalService.getPaymentInfos(integrator, getPaymentProvider(), merchantId);
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getDefaultPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final String merchantId) {

        return tokenizedPaymentInfoRetrievalService.getDefaultPaymentInfo(integrator, getPaymentProvider(), merchantId);
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getCartPaymentInfo(
        @NonNull final AbstractOrderModel cart,
        @NonNull final String merchantId) {

        return tokenizedPaymentInfoRetrievalService.getCartPaymentInfo(cart, getPaymentProvider(), merchantId);
    }

    @Override
    public TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final TokenizedSepaDirectDebitPaymentInfoData paymentInfoData) {

        return tokenizedPaymentInfoManagementService.createPaymentInfo(integrator, paymentInfoData);
    }

    @Override
    public void removePaymentInfo(@NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        tokenizedPaymentInfoManagementService.removePaymentInfo(paymentInfo);
    }

    @Override
    public void populatePaymentInfoData(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo,
        @NonNull final TokenizedSepaDirectDebitPaymentInfoData paymentInfoData) {

        tokenizedPaymentInfoManagementService.populatePaymentInfoData(paymentInfo, paymentInfoData);
    }

    public void assignMerchantId(@NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo, @NonNull final String merchantId) {
        tokenizedPaymentInfoManagementService.assignMerchantId(paymentInfo, merchantId);
    }

    public void assignMandateReference(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo,
        @NonNull final String mandateReference) {

        tokenizedPaymentInfoManagementService.assignMandateReference(paymentInfo, mandateReference);
    }

    public void updateAfterTokenization(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo,
        @NonNull final TokenizationResult tokenizationResult) {

        final TokenizationDetails tokenizationDetails = TokenizationDetails.of(
            tokenizationResult.storedPaymentMethodId(),
            tokenizationResult.pspReference()
        );
        tokenizedPaymentInfoManagementService.updateAfterTokenization(paymentInfo, tokenizationDetails);
    }

    public void updateAfterMandateActivation(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo,
        @NonNull final MandateDetails mandateDetails) {

        tokenizedPaymentInfoManagementService.updateAfterMandateActivation(paymentInfo, mandateDetails);
    }

    public void finalizePaymentInfo(@NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        tokenizedPaymentInfoManagementService.finalizePaymentInfo(paymentInfo);
    }

    @Override
    public PaymentProvider getPaymentProvider() {
        return ADYEN;
    }
}
