package com.sast.cis.payment.adyen.service


import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.license.ProductSellerProvider
import com.sast.cis.payment.adyen.model.AdyenSellerAccountModel
import com.sast.cis.payment.adyen.service.selleraccount.AdyenSellerAccountProvider
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.testframework.JUnitPlatformSpecification

import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.enums.PaymentProvider.BOSCH_TRANSFER

@UnitTest
class AdyenPaymentServiceUnitSpec extends JUnitPlatformSpecification {

    private AdyenPaymentMethodDispatch adyenPaymentMethodDispatch = Mock()
    private AdyenSellerAccountProvider adyenSellerAccountProvider = Mock()
    private ProductSellerProvider productSellerProvider = Mock(ProductSellerProvider)
    private AdyenPaymentService paymentService

    private IoTCompanyModel buyerCompany = Mock()
    private IoTCompanyModel sellerCompany = Mock()
    private AdyenSellerAccountModel adyenSellerAccount = Mock()
    private CountryModel country = Mock()

    private AppLicenseModel license1 = Mock()
    private AppLicenseModel license2 = Mock()

    def setup() {
        paymentService = new AdyenPaymentService(adyenPaymentMethodDispatch, adyenSellerAccountProvider, productSellerProvider)

        buyerCompany.getCountry() >> country
        country.getSupportedPaymentProviders() >> [ADYEN]

        productSellerProvider.getSellerOrThrow(license1) >> sellerCompany
        productSellerProvider.getSellerOrThrow(license2) >> sellerCompany
        adyenPaymentMethodDispatch.supportsPaymentMethod(SEPA_DIRECTDEBIT) >> true
        adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.of(adyenSellerAccount)
    }

    def "supportsPurchase should return true when all conditions are met"() {
        when:
        def result = paymentService.supportsPurchase(SEPA_DIRECTDEBIT, buyerCompany, [license1, license2] as Set)

        then:
        result
    }

    def "supportsPurchase should return false when payment provider is unsupported in buyer's country"() {
        when:
        def result = paymentService.supportsPurchase(SEPA_DIRECTDEBIT, buyerCompany, [license1] as Set)

        then:
        country.getSupportedPaymentProviders() >> [BOSCH_TRANSFER]
        !result
    }

    def "supportsPurchase should return false when seller company does not a seller account"() {
        when:
        def result = paymentService.supportsPurchase(SEPA_DIRECTDEBIT, buyerCompany, [license1] as Set)

        then:
        adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !result
    }

    def "supportsSale should return true when payment method is supported and seller has active Adyen account"() {
        when:
        def result = paymentService.supportsSale(SEPA_DIRECTDEBIT, sellerCompany)

        then:
        result
    }

    def "supportsSale should return false when payment method is unsupported"() {
        when:
        def result = paymentService.supportsSale(SEPA_DIRECTDEBIT, sellerCompany)

        then:
        adyenPaymentMethodDispatch.supportsPaymentMethod(SEPA_DIRECTDEBIT) >> false
        !result
    }

    def "supportsSale should return false when seller has no active Adyen account"() {
        when:
        def result = paymentService.supportsSale(SEPA_DIRECTDEBIT, sellerCompany)

        then:
        adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !result
    }

    def "isPayoutAccountValidated should return true when seller has active Adyen account"() {
        when:
        def result = paymentService.isPayoutAccountValidated(sellerCompany)

        then:
        result
    }

    def "isPayoutAccountValidated should return false when seller has no active Adyen account"() {
        when:
        def result = paymentService.isPayoutAccountValidated(sellerCompany)

        then:
        adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !result
    }
}
