package com.sast.cis.payment.adyen.service.directdebit.tokenization

import com.sast.adyengateway.dto.TokenizationResponseDto
import com.sast.cis.core.dao.PaymentTransactionDao
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.payment.adyen.client.tokenization.TokenizationException
import com.sast.cis.payment.adyen.client.util.AdyenGatewayWiremockRule
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import org.junit.Rule

import javax.annotation.Resource

import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.util.Base58UUIDCodeGenerator.generateCode
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_INTEGRATOR_UID
import static de.hybris.platform.payment.dto.TransactionStatus.ACCEPTED
import static de.hybris.platform.payment.dto.TransactionStatus.ERROR
import static de.hybris.platform.payment.enums.PaymentTransactionType.TOKENIZATION
import static java.util.UUID.randomUUID
import static org.assertj.core.util.DateUtil.now

@IntegrationTest
class AdyenSepaDirectDebitTokenizationServiceISpec extends ServicelayerTransactionalSpockSpecification {

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Rule
    public AdyenGatewayWiremockRule adyenGatewayWiremockRule = new AdyenGatewayWiremockRule()

    @Resource
    private ModelService modelService

    @Resource
    private PaymentTransactionDao paymentTransactionDao

    @Resource
    private AdyenSepaDirectDebitTokenizationService adyenSepaDirectDebitTokenizationService

    private TokenizedSepaDirectDebitPaymentInfoModel tokenizedSepaDirectDebitPaymentInfo

    def setup() {
        def paymentInfoOwner = sampleDataCreator.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID)

        tokenizedSepaDirectDebitPaymentInfo = createPaymentInfo(paymentInfoOwner)
    }

    def "tokenize payment info should create a tokenization tx and update the payment info with tokenization details"() {
        given:
        def expectedTokenizationResponse = TokenizationResponseDto.builder()
                .resultCode("success")
                .pspReference(generateCode("pspReference"))
                .storedPaymentMethodId(generateCode("token"))
                .build()
        adyenGatewayWiremockRule.prepareTokenizationResponse(expectedTokenizationResponse)

        when:
        adyenSepaDirectDebitTokenizationService.tokenizePaymentInfo(tokenizedSepaDirectDebitPaymentInfo)

        then:
        tokenizedSepaDirectDebitPaymentInfo.recurringReference == expectedTokenizationResponse.storedPaymentMethodId()
        tokenizedSepaDirectDebitPaymentInfo.pspTokenizationReference == expectedTokenizationResponse.pspReference()

        and:
        def transactions = paymentTransactionDao.getTransactionByPaymentProviderAndType(ADYEN, TOKENIZATION)
        transactions.size() == 1
        def tokenizationTransaction = transactions[0]
        tokenizationTransaction.requestToken == expectedTokenizationResponse.pspReference()
        tokenizationTransaction.entries.size() == 1
        tokenizationTransaction.entries[0].transactionStatus == ACCEPTED.name()
    }

    def "should create a failed tokenization transaction when the request to the Adyen Gateway fails"() {
        given:
        adyenGatewayWiremockRule.prepareTokenizationResponse(500)

        when:
        adyenSepaDirectDebitTokenizationService.tokenizePaymentInfo(tokenizedSepaDirectDebitPaymentInfo)

        then:
        thrown(TokenizationException)
        def transactions = paymentTransactionDao.getTransactionByPaymentProviderAndType(ADYEN, TOKENIZATION)
        transactions.size() == 1
        def tokenizationTransaction = transactions[0]
        !tokenizationTransaction.requestToken
        tokenizationTransaction.entries.size() == 1
        tokenizationTransaction.entries[0].transactionStatus == ERROR.name()
    }

    TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(final IntegratorModel owner) {
        def mandate = TokenizedSepaDirectDebitPaymentInfoBuilder.generate()
                .withPaymentProvider(ADYEN)
                .withCode(randomUUID().toString())
                .withPaymentProvider(ADYEN)
                .withPgwMerchantId("merchant_id")
                .withShopperReference(generateCode('shopperReference'))
                .withAccountHolderName(generateCode('testAccountHolder'))
                .withIBAN(generateCode('testIBAN'))
                .withMandateReference("sepa-mandate-reference")
                .withDateOfSignature(now())
                .withUser(owner)
                .withCompanyScope(true)
                .withSaved(false)
                .withDuplicate(false)
                .buildIntegrationInstance()
        modelService.save(mandate)
        return mandate
    }
}
