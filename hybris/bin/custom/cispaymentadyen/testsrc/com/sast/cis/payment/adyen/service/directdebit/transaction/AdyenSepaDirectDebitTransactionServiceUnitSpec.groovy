package com.sast.cis.payment.adyen.service.directdebit.transaction

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.payment.adyen.client.tokenization.TokenizationResult
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification

import static de.hybris.platform.payment.dto.TransactionStatus.*
import static de.hybris.platform.payment.dto.TransactionStatusDetails.REJECTED_BY_PSP
import static de.hybris.platform.payment.dto.TransactionStatusDetails.SUCCESFULL
import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION
import static de.hybris.platform.payment.enums.PaymentTransactionType.TOKENIZATION
import static java.math.BigDecimal.ZERO

@UnitTest
class AdyenSepaDirectDebitTransactionServiceUnitSpec extends JUnitPlatformSpecification {

    private AdyenTransactionFactory adyenTransactionFactory = Mock()
    private ModelService modelService = Mock()

    private AdyenSepaDirectDebitTransactionService service

    private AbstractOrderModel order = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private PaymentTransactionModel authTransaction = Mock()
    private PaymentTransactionModel tokenizationTransaction = Mock()
    private PaymentTransactionEntryModel authEntry = Mock()
    private PaymentTransactionEntryModel tokenizationEntry = Mock()

    private final String authTxCode = "auth-tx"
    private final String authTxEntryCode = "auth-entry"


    private final String tokenizationTxCode = "tokenization-tx"
    private final String tokenizationTxEntryCode = "tokenization-entry"

    void setup() {
        service = new AdyenSepaDirectDebitTransactionService(adyenTransactionFactory, modelService)

        order.getCode() >> "order-123"
        order.getPaymentInfo() >> paymentInfo

        authTransaction.getCode() >> authTxCode
        authTransaction.getType() >> AUTHORIZATION
        authTransaction.getEntries() >> [authEntry]

        authEntry.getType() >> AUTHORIZATION
        authEntry.getCode() >> authTxEntryCode
        authEntry.getTransactionStatus() >> PENDING.name()
        authEntry.getPaymentTransaction() >> authTransaction

        tokenizationTransaction.getCode() >> tokenizationTxCode
        tokenizationTransaction.getType() >> TOKENIZATION
        tokenizationTransaction.getEntries() >> [tokenizationEntry]

        tokenizationEntry.getType() >> TOKENIZATION
        tokenizationEntry.getCode() >> tokenizationTxEntryCode
        tokenizationEntry.getTransactionStatus() >> PENDING.name()
        tokenizationEntry.getPaymentTransaction() >> tokenizationTransaction

    }

    def "initiateAuthorization creates an auth transaction with a pending entry"() {
        when:
        def result = service.initiateAuthorization(order)

        then:
        1 * adyenTransactionFactory.createTransaction(AUTHORIZATION, order, ZERO) >> authTransaction
        1 * adyenTransactionFactory.createEntry(authTransaction, AUTHORIZATION, PENDING) >> authEntry
        result == authTransaction
    }

    def "when saveAuthorizationSuccess then update tx entry status"() {
        when:
        service.saveAuthorizationSuccess(authTransaction)

        then:
        1 * authEntry.setTransactionStatus(ACCEPTED.name())
        1 * authEntry.setTransactionStatusDetails(SUCCESFULL.name())
        1 * modelService.save(authEntry)
        1 * modelService.refresh(authTransaction)
    }

    def "saveAuthorizationSuccess throws IllegalStateException when transaction type is not an authorization"() {
        when:
        service.saveAuthorizationSuccess(tokenizationTransaction)

        then:
        def exception = thrown(IllegalStateException)
        exception.message == "Given tx '${tokenizationTxCode}' is not of type AUTHORIZATION"
    }

    def "saveAuthorizationSuccess throws IllegalStateException when entry is not PENDING"() {
        when:
        service.saveAuthorizationSuccess(authTransaction)

        then:
        authEntry.getTransactionStatus() >> REJECTED.name()
        def exception = thrown(IllegalStateException)
        exception.message.contains("Expected transaction entry '${authTxEntryCode}' to be in status PENDING")
    }

    def "initiateTokenizationTransaction creates a tokenization transaction with a pending entry"() {
        when:
        def result = service.initiateTokenizationTransaction(paymentInfo)

        then:
        1 * adyenTransactionFactory.createTransaction(TOKENIZATION, paymentInfo) >> tokenizationTransaction
        1 * adyenTransactionFactory.createEntry(tokenizationTransaction, TOKENIZATION, PENDING) >> tokenizationEntry
        result == tokenizationTransaction
    }

    def "when saveTokenizationSuccess then update tx entry status"() {
        given:
        def expectedTokenizationResult = tokenizationResult()

        when:
        service.saveTokenizationSuccess(tokenizationTransaction, expectedTokenizationResult)

        then:
        1 * tokenizationEntry.setTransactionStatus(ACCEPTED.name())
        1 * tokenizationEntry.setTransactionStatusDetails(SUCCESFULL.name())
        1 * tokenizationTransaction.setRequestToken(expectedTokenizationResult.pspReference())
        1 * modelService.save(tokenizationEntry)
        1 * modelService.refresh(tokenizationTransaction)
    }

    def "when saveTokenizationFailure then update tx entry status"() {
        given:
        def transactionStatusDetails = REJECTED_BY_PSP

        when:
        service.saveTokenizationFailure(tokenizationTransaction, transactionStatusDetails)

        then:
        1 * tokenizationEntry.setTransactionStatus(ERROR.name())
        1 * tokenizationEntry.setTransactionStatusDetails(transactionStatusDetails.name())
        1 * modelService.save(tokenizationEntry)
        1 * modelService.refresh(tokenizationTransaction)
    }

    def "saveTokenizationSuccess throws IllegalStateException when transaction type is not a tokenization"() {
        when:
        service.saveTokenizationSuccess(authTransaction, tokenizationResult())

        then:
        def exception = thrown(IllegalStateException)
        exception.message == "Given tx '${authTxCode}' is not of type TOKENIZATION"
    }

    def "saveTokenizationSuccess throws IllegalStateException when entry is not PENDING"() {
        when:
        service.saveTokenizationSuccess(tokenizationTransaction, tokenizationResult())

        then:
        tokenizationEntry.getTransactionStatus() >> REJECTED.name()
        def exception = thrown(IllegalStateException)
        exception.message.contains("Expected transaction entry '${tokenizationTxEntryCode}' to be in status PENDING")
    }

    TokenizationResult tokenizationResult() {
        return TokenizationResult.of('token', 'psp-reference', 'success')
    }
}
