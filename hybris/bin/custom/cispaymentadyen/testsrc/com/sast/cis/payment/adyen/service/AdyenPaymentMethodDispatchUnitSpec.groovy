package com.sast.cis.payment.adyen.service

import com.sast.cis.core.data.PaymentInfoData
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.AchInternationalCreditTransferPaymentInfoModel
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter
import com.sast.cis.core.paymentintegration.data.AuthorizationResult
import com.sast.cis.core.paymentintegration.data.CheckoutInfo
import com.sast.cis.core.paymentintegration.exception.PaymentInfoNotSupportedByProviderException
import com.sast.cis.core.paymentintegration.exception.PaymentMethodNotSupportedByProviderException
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoModel
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.AchInternationalCreditTransferPaymentInfoBuilder
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import generated.de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoBuilder
import org.junit.Test
import spock.lang.Unroll

import static com.sast.cis.core.enums.PaymentMethodType.*
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.enums.PaymentProvider.ZERO
import static java.math.BigDecimal.TEN

@UnitTest
class AdyenPaymentMethodDispatchUnitSpec extends JUnitPlatformSpecification {

    private AdyenPaymentMethodStrategy sepaDirectDebitStrategy = Mock()
    private AdyenPaymentMethodStrategy creditCardStrategy = Mock()

    private AdyenPaymentMethodDispatch paymentMethodDispatch

    private AbstractOrderModel abstractOrder = Mock()
    private CheckoutInfo mockCheckoutInfo = Mock()
    private PaymentTransactionModel mockTx = Mock()
    private PaymentTransactionEntryModel mockTxEntry = Mock()
    private IntegratorModel integrator = Mock()

    private TokenizedSepaDirectDebitPaymentInfoModel sepaMandate
    private CreditCardPaymentInfoModel creditCard
    private CreditCardPaymentInfoModel wrongPspCc
    private AchInternationalCreditTransferPaymentInfoModel achInfo

    def setup() {
        sepaDirectDebitStrategy.getPaymentMethod() >> SEPA_DIRECTDEBIT
        sepaDirectDebitStrategy.getPaymentInfoType() >> TokenizedSepaDirectDebitPaymentInfoModel.class
        creditCardStrategy.getPaymentMethod() >> CREDIT_CARD
        creditCardStrategy.getPaymentInfoType() >> CreditCardPaymentInfoModel.class

        paymentMethodDispatch = new AdyenPaymentMethodDispatch(Set.of(sepaDirectDebitStrategy, creditCardStrategy))

        sepaMandate = TokenizedSepaDirectDebitPaymentInfoBuilder.generate().withPaymentProvider(ADYEN).buildInstance()
        achInfo = AchInternationalCreditTransferPaymentInfoBuilder.generate().withPaymentProvider(ADYEN).buildInstance()
        creditCard = CreditCardPaymentInfoBuilder.generate().withPaymentProvider(ADYEN).buildInstance()
        wrongPspCc = CreditCardPaymentInfoBuilder.generate().withPaymentProvider(ZERO).buildInstance()

        mockTxEntry.getPaymentTransaction() >> mockTx
    }

    @Test
    void 'constructor throws exception if strategies return the same payment method'() {
        when:
        new AdyenPaymentMethodDispatch(Set.of(sepaDirectDebitStrategy, creditCardStrategy))

        then:
        creditCardStrategy.getPaymentMethod() >> SEPA_DIRECTDEBIT
        thrown(IllegalArgumentException)
    }

    @Test
    void 'constructor throws exception if strategies return the same payment info type'() {
        when:
        new AdyenPaymentMethodDispatch(Set.of(sepaDirectDebitStrategy, creditCardStrategy))

        then:
        creditCardStrategy.getPaymentInfoType() >> TokenizedSepaDirectDebitPaymentInfoModel.class
        thrown(IllegalArgumentException)
    }

    @Test
    void 'prepareCheckout delegates to appropriate strategy'() {
        given:
        def authParameter = defaultAuthParameter

        when:
        def sepaCheckoutInfo = paymentMethodDispatch.prepareCheckout(authParameter, SEPA_DIRECTDEBIT)

        then:
        1 * sepaDirectDebitStrategy.prepareCheckout(authParameter) >> mockCheckoutInfo
        0 * creditCardStrategy.prepareCheckout(_)
        sepaCheckoutInfo == mockCheckoutInfo

        when:
        def ccCheckoutInfo = paymentMethodDispatch.prepareCheckout(authParameter, CREDIT_CARD)

        then:
        1 * creditCardStrategy.prepareCheckout(authParameter) >> mockCheckoutInfo
        0 * sepaDirectDebitStrategy.prepareCheckout(_)
        ccCheckoutInfo == mockCheckoutInfo
    }

    @Test
    void 'prepareCheckout throws exception for unknown payment method'() {
        given:
        def authParameter = defaultAuthParameter

        when:
        paymentMethodDispatch.prepareCheckout(authParameter, ACH_INTERNATIONAL)

        then:
        thrown(PaymentMethodNotSupportedByProviderException)
    }


    @Test
    void 'confirmSelectedPaymentInfo delegates to appropriate payment method'() {
        given:
        def authParameter = defaultAuthParameter

        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(authParameter)

        then:
        abstractOrder.getPaymentInfo() >> sepaMandate
        1 * sepaDirectDebitStrategy.confirmSelectedPaymentInfo(authParameter)
        0 * creditCardStrategy.confirmSelectedPaymentInfo(authParameter)

        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(authParameter)

        then:
        abstractOrder.getPaymentInfo() >> creditCard
        0 * sepaDirectDebitStrategy.confirmSelectedPaymentInfo(authParameter)
        1 * creditCardStrategy.confirmSelectedPaymentInfo(authParameter)
    }

    @Test
    void 'authorize delegates to appropriate payment method'() {
        given:
        def authParameter = defaultAuthParameter

        when:
        def sepaTxEntry = paymentMethodDispatch.authorize(authParameter)

        then:
        abstractOrder.getPaymentInfo() >> sepaMandate
        1 * sepaDirectDebitStrategy.authorize(authParameter) >> mockTxEntry
        0 * creditCardStrategy.authorize(authParameter)
        sepaTxEntry == mockTxEntry

        when:
        def ccTxEntry = paymentMethodDispatch.authorize(authParameter)

        then:
        abstractOrder.getPaymentInfo() >> creditCard
        0 * sepaDirectDebitStrategy.authorize(authParameter)
        1 * creditCardStrategy.authorize(authParameter) >> mockTxEntry
        ccTxEntry == mockTxEntry
    }

    @Test
    void 'getAuthorizationResult delegates to appropriate payment method'() {
        given:
        def givenAuthResult = new AuthorizationResult()

        when:
        def sepaAuthResult = paymentMethodDispatch.getAuthorizationResult(mockTxEntry)

        then:
        mockTx.getInfo() >> sepaMandate
        1 * sepaDirectDebitStrategy.getAuthorizationResult(mockTxEntry) >> givenAuthResult
        0 * creditCardStrategy.getAuthorizationResult(mockTxEntry)
        sepaAuthResult == givenAuthResult

        when:
        def ccAuthResult = paymentMethodDispatch.getAuthorizationResult(mockTxEntry)

        then:
        mockTx.getInfo() >> creditCard
        0 * sepaDirectDebitStrategy.getAuthorizationResult(mockTxEntry)
        1 * creditCardStrategy.getAuthorizationResult(mockTxEntry) >> givenAuthResult
        ccAuthResult == givenAuthResult
    }


    @Test
    void 'confirmSelectedPaymentInfo throws exception for unknown payment info type'() {
        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(defaultAuthParameter)

        then:
        abstractOrder.getPaymentInfo() >> achInfo
        thrown(PaymentInfoNotSupportedByProviderException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    void 'confirmSelectedPaymentInfo throws exception for payment info with wrong payment provider'() {
        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(defaultAuthParameter)

        then:
        abstractOrder.getPaymentInfo() >> wrongPspCc
        thrown(IllegalStateException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    @Unroll
    void 'supportsPaymentMethod returns true for supported payment methods'() {
        when:
        def actualSupported = paymentMethodDispatch.supportsPaymentMethod(givenPaymentMethod)

        then:
        actualSupported

        where:
        givenPaymentMethod << [SEPA_DIRECTDEBIT, CREDIT_CARD]
    }

    @Test
    @Unroll
    void 'supportsPaymentMethod returns false for unsupported payment methods'() {
        when:
        def actualSupported = paymentMethodDispatch.supportsPaymentMethod(givenPaymentMethod as PaymentMethodType)

        then:
        !actualSupported

        where:
        givenPaymentMethod << (PaymentMethodType.values() - [SEPA_DIRECTDEBIT, CREDIT_CARD])
    }

    @Test
    void 'createPaymentInfo delegates to appropriate payment method'() {
        given:
        def givenSepaInfoData = new PaymentInfoData()
        givenSepaInfoData.setPaymentMethod(SEPA_DIRECTDEBIT)
        givenSepaInfoData.setPaymentProvider(ADYEN)
        def givenCcInfoData = new PaymentInfoData()
        givenCcInfoData.setPaymentMethod(CREDIT_CARD)
        givenCcInfoData.setPaymentProvider(ADYEN)

        when:
        def sepaInfoModel = paymentMethodDispatch.createPaymentInfo(integrator, givenSepaInfoData)

        then:
        1 * sepaDirectDebitStrategy.createPaymentInfo(integrator, givenSepaInfoData) >> sepaMandate
        0 * creditCardStrategy.createPaymentInfo(_, _)
        sepaInfoModel == sepaMandate

        when:
        def ccInfoModel = paymentMethodDispatch.createPaymentInfo(integrator, givenCcInfoData)

        then:
        1 * creditCardStrategy.createPaymentInfo(integrator, givenCcInfoData) >> creditCard
        0 * sepaDirectDebitStrategy.createPaymentInfo(_, _)
        ccInfoModel == creditCard
    }

    @Test
    @Unroll
    void 'createPaymentInfo throws if given info data is for wrong payment provider'() {
        given:
        def givenInfoData = new PaymentInfoData()
        givenInfoData.setPaymentMethod(SEPA_DIRECTDEBIT)
        givenInfoData.setPaymentProvider(givenPaymentProvider as PaymentProvider)

        when:
        paymentMethodDispatch.createPaymentInfo(integrator, givenInfoData)

        then:
        thrown(IllegalStateException)

        where:
        givenPaymentProvider << (PaymentProvider.values() - ADYEN)
    }


    @Test
    @Unroll
    void 'createPaymentInfo throws if given info data is for unknown payment method'() {
        given:
        def givenInfoData = new PaymentInfoData()
        givenInfoData.setPaymentMethod(givenPaymentMethod as PaymentMethodType)
        givenInfoData.setPaymentProvider(ADYEN)

        when:
        paymentMethodDispatch.createPaymentInfo(integrator, givenInfoData)

        then:
        thrown(PaymentMethodNotSupportedByProviderException)

        where:
        givenPaymentMethod << (values() - [CREDIT_CARD, SEPA_DIRECTDEBIT])
    }

    @Test
    void 'authorize throws for unknown payment info type'() {
        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(defaultAuthParameter)

        then:
        abstractOrder.getPaymentInfo() >> achInfo
        thrown(PaymentInfoNotSupportedByProviderException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    void 'authorize throws for payment info with wrong payment provider'() {
        when:
        paymentMethodDispatch.confirmSelectedPaymentInfo(defaultAuthParameter)

        then:
        abstractOrder.getPaymentInfo() >> wrongPspCc
        thrown(IllegalStateException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    void 'getAuthorizationResult throws for unknown payment info type'() {
        when:
        paymentMethodDispatch.getAuthorizationResult(mockTxEntry)

        then:
        mockTx.getInfo() >> achInfo
        thrown(PaymentInfoNotSupportedByProviderException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    void 'getAuthorizationResult throws for payment info with wrong payment provider'() {
        when:
        paymentMethodDispatch.getAuthorizationResult(mockTxEntry)

        then:
        mockTx.getInfo() >> wrongPspCc
        thrown(IllegalStateException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }

    @Test
    void 'getAuthorizationResult throws for tx entry without transaction'() {
        when:
        paymentMethodDispatch.getAuthorizationResult(mockTxEntry)

        then:
        mockTxEntry.getPaymentTransaction() >> null
        thrown(IllegalStateException)
        0 * sepaDirectDebitStrategy._
        0 * creditCardStrategy._
    }


    private AuthorizationParameter getDefaultAuthParameter() {
        AuthorizationParameter.builder()
                .abstractOrder(abstractOrder).plannedAmount(TEN).build()
    }
}
