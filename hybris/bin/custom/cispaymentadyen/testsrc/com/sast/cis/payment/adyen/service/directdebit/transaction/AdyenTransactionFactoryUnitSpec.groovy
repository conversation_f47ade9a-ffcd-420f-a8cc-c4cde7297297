package com.sast.cis.payment.adyen.service.directdebit.transaction

import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.service.AbstractOrderHashService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.time.TimeService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.de.hybris.platform.payment.model.PaymentTransactionBuilder
import generated.de.hybris.platform.payment.model.PaymentTransactionEntryBuilder
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static de.hybris.platform.payment.dto.TransactionStatus.PENDING
import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION
import static de.hybris.platform.payment.enums.PaymentTransactionType.TOKENIZATION
import static java.math.BigDecimal.ZERO
import static org.assertj.core.util.DateUtil.now

@UnitTest
class AdyenTransactionFactoryUnitSpec extends JUnitPlatformSpecification {

    private static final String MERCHANT_ID = 'merchant_id_123456'
    private static final String ORDER_HASH = 'order_hash_123456'
    private static final Date NOW = now()

    private ModelService modelService = Mock()
    private TimeService timeService = Mock()
    private AbstractOrderHashService abstractOrderHashService = Mock()

    private AdyenTransactionFactory adyenTransactionFactory

    private AbstractOrderModel order = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private CurrencyModel currency = Mock()

    private PaymentTransactionModel existingTx = Mock()

    def setup() {
        adyenTransactionFactory = new AdyenTransactionFactory(modelService, timeService, abstractOrderHashService)

        order.getPaymentInfo() >> paymentInfo
        order.getCurrency() >> currency
        paymentInfo.getPgwMerchantId() >> MERCHANT_ID
        paymentInfo.getPaymentProvider() >> ADYEN

        modelService.create(PaymentTransactionModel.class) >> { args ->
            return PaymentTransactionBuilder.generate().buildInstance()
        }

        modelService.create(PaymentTransactionEntryModel.class) >> { args ->
            return PaymentTransactionEntryBuilder.generate().buildInstance()
        }

        existingTx.getOrder() >> order
        existingTx.getCurrency() >> currency

        timeService.getCurrentTime() >> NOW
        abstractOrderHashService.calculateHash(order) >> ORDER_HASH
    }

    @Test
    void 'createTransaction with an order persists a new transaction'() {
        given:
        def givenType = AUTHORIZATION
        def givenAmount = ZERO

        when:
        def actualTransaction = adyenTransactionFactory.createTransaction(givenType, order, givenAmount)

        then:
        verifyAll(actualTransaction) {
            code.startsWith('AdyenTx')
            type == givenType
            order == order
            info == info
            paymentProvider == ADYEN.code
            pgwMerchantId == MERCHANT_ID
            plannedAmount == givenAmount
            currency == currency
        }
        1 * modelService.save(_ as PaymentTransactionModel)
        1 * modelService.refresh(order)
    }

    @Test
    void 'createTransaction with a payment info persists a new transaction'() {
        given:
        def givenType = TOKENIZATION

        when:
        def actualTransaction = adyenTransactionFactory.createTransaction(givenType, paymentInfo)

        then:
        verifyAll(actualTransaction) {
            code.startsWith('AdyenTx')
            type == givenType
            info == info
            paymentProvider == ADYEN.code
            pgwMerchantId == MERCHANT_ID
            !order
        }
        1 * modelService.save(_ as PaymentTransactionModel)
        1 * modelService.refresh(paymentInfo)
    }

    @Test
    void 'createTransaction throws an exception if given order has no payment info'() {
        when:
        adyenTransactionFactory.createTransaction(AUTHORIZATION, order, ZERO)

        then:
        order.getPaymentInfo() >> null
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws an exception if given order payment info has no merchant id'() {
        when:
        adyenTransactionFactory.createTransaction(AUTHORIZATION, order, ZERO)

        then:
        paymentInfo.getPgwMerchantId() >> ''
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws an exception if given order payment info has wrong payment provider'() {
        when:
        adyenTransactionFactory.createTransaction(AUTHORIZATION, order, ZERO)

        then:
        paymentInfo.getPaymentProvider() >> PaymentProvider.ZERO
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createEntry creates a new entry with given data for given transaction'() {
        when:
        def actualEntry = adyenTransactionFactory.createEntry(existingTx, AUTHORIZATION, PENDING)

        then:
        verifyAll(actualEntry) {
            code.startsWith('AdyenTxEntry')
            paymentTransaction == existingTx
            time == NOW
            transactionStatus == PENDING.name()
            type == AUTHORIZATION
            currency == currency
            cartHash == ORDER_HASH
        }
        1 * modelService.save(_ as PaymentTransactionEntryModel)
        1 * modelService.refresh(existingTx)
    }
}
