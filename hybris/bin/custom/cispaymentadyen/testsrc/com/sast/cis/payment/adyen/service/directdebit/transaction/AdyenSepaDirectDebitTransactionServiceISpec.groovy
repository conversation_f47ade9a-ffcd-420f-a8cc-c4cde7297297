package com.sast.cis.payment.adyen.service.directdebit.transaction

import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.payment.adyen.client.tokenization.TokenizationResult
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.order.CartService
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.services.BaseStoreService
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.core.constants.BaseStoreEnum.AA
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.util.Base58UUIDCodeGenerator.generateCode
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_INTEGRATOR_UID
import static de.hybris.platform.payment.dto.TransactionStatus.*
import static de.hybris.platform.payment.dto.TransactionStatusDetails.REJECTED_BY_PSP
import static de.hybris.platform.payment.dto.TransactionStatusDetails.SUCCESFULL
import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION
import static de.hybris.platform.payment.enums.PaymentTransactionType.TOKENIZATION
import static java.util.UUID.randomUUID
import static org.assertj.core.util.DateUtil.now

@IntegrationTest
class AdyenSepaDirectDebitTransactionServiceISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private CartService cartService

    @Resource
    private UserService userService

    @Resource
    private IntegratorService integratorService

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private ModelService modelService

    @Resource
    private AdyenSepaDirectDebitTransactionService adyenSepaDirectDebitTransactionService

    private IntegratorModel integrator
    private TokenizedSepaDirectDebitPaymentInfoModel tokenizedPaymentInfo
    private CartModel cart

    def setup() {
        integrator = integratorService.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID)

        userService.setCurrentUser(integrator)
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(AA.getBaseStoreUid()), false)

        tokenizedPaymentInfo = createPaymentInfo(integrator)
        cart = cartService.getSessionCart()
        cart.setPaymentInfo(tokenizedPaymentInfo)
    }

    @Test
    def 'initiate authorization creates an auth transaction and pending entry'() {
        when:
        def authorization = adyenSepaDirectDebitTransactionService.initiateAuthorization(cart)

        then:
        cart.paymentTransactions == [authorization]
        with(authorization) {
            verifyAll {
                type == AUTHORIZATION
                paymentProvider == ADYEN.code
            }
        }

        def authEntry = authorization.entries[0]

        with(authEntry) {
            verifyAll {
                type == AUTHORIZATION
                transactionStatus == PENDING.name()
            }
        }
    }

    @Test
    def 'save authorization success sets the auth entry status to SUCCESS'() {
        given:
        def authorizationTx = adyenSepaDirectDebitTransactionService.initiateAuthorization(cart)

        when:
        def authorizationEntry = adyenSepaDirectDebitTransactionService.saveAuthorizationSuccess(authorizationTx)

        then:
        with(authorizationEntry) {
            verifyAll {
                type == AUTHORIZATION
                transactionStatus == ACCEPTED.name()
                transactionStatusDetails == SUCCESFULL.name()
            }
        }
    }

    @Test
    def 'initiate tokenization creates a tokenization transaction and pending entry'() {
        when:
        def tokenizationTx = adyenSepaDirectDebitTransactionService.initiateTokenizationTransaction(tokenizedPaymentInfo)

        then:
        with(tokenizationTx) {
            verifyAll {
                type == TOKENIZATION
                paymentProvider == ADYEN.code
                info == tokenizedPaymentInfo
            }
        }

        def tokenizationEntry = tokenizationTx.entries[0]

        with(tokenizationEntry) {
            verifyAll {
                type == TOKENIZATION
                transactionStatus == PENDING.name()
            }
        }
    }

    @Test
    def 'save tokenization success sets the tokenization entry status to SUCCESS'() {
        given:
        def tokenizationResult = tokenizationResult()
        def tokenizationTx = adyenSepaDirectDebitTransactionService.initiateTokenizationTransaction(tokenizedPaymentInfo)

        when:
        def tokenizationEntry = adyenSepaDirectDebitTransactionService.saveTokenizationSuccess(tokenizationTx, tokenizationResult)

        then:
        with(tokenizationEntry) {
            verifyAll {
                type == TOKENIZATION
                transactionStatus == ACCEPTED.name()
                transactionStatusDetails == SUCCESFULL.name()
                with(tokenizationEntry.paymentTransaction) {
                    verifyAll {
                        requestToken == tokenizationResult.pspReference()
                    }
                }
            }
        }
    }

    @Test
    def 'save tokenization failure sets the tokenization entry status to ERROR'() {
        given:
        def tokenizationTx = adyenSepaDirectDebitTransactionService.initiateTokenizationTransaction(tokenizedPaymentInfo)

        when:
        def tokenizationEntry = adyenSepaDirectDebitTransactionService.saveTokenizationFailure(tokenizationTx, REJECTED_BY_PSP)

        then:
        with(tokenizationEntry) {
            verifyAll {
                type == TOKENIZATION
                transactionStatus == ERROR.name()
                transactionStatusDetails == REJECTED_BY_PSP.name()
            }
        }
    }

    TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(final IntegratorModel user) {
        def mandate = TokenizedSepaDirectDebitPaymentInfoBuilder.generate()
                .withCode(randomUUID().toString())
                .withPaymentProvider(ADYEN)
                .withPgwMerchantId("merchant_id")
                .withAccountHolderName(generateCode('testAccountHolder'))
                .withIBAN(generateCode('testIBAN'))
                .withUser(user)
                .withCreationtime(now())
                .withSaved(true)
                .withDuplicate(false)
                .withCompanyScope(true)
                .buildIntegrationInstance()
        modelService.save(mandate)
        return mandate
    }

    TokenizationResult tokenizationResult() {
        return TokenizationResult.of(generateCode('token'), generateCode('pspReference'), 'success')
    }
}
