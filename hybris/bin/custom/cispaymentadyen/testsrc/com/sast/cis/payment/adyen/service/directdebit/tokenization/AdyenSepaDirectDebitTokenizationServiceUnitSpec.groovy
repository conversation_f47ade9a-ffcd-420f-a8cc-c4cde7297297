package com.sast.cis.payment.adyen.service.directdebit.tokenization

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.payment.adyen.client.tokenization.AdyenGatewayTokenizationFacade
import com.sast.cis.payment.adyen.client.tokenization.TokenizationException
import com.sast.cis.payment.adyen.client.tokenization.TokenizationResult
import com.sast.cis.payment.adyen.service.directdebit.AdyenTokenizedSepaDirectDebitPaymentInfoService
import com.sast.cis.payment.adyen.service.directdebit.transaction.AdyenSepaDirectDebitTransactionService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class AdyenSepaDirectDebitTokenizationServiceUnitSpec extends JUnitPlatformSpecification {

    private AdyenSepaDirectDebitTransactionService transactionService = Mock()
    private AdyenGatewayTokenizationFacade tokenizationFacade = Mock()
    private AdyenTokenizedSepaDirectDebitPaymentInfoService paymentInfoService = Mock()

    private AdyenSepaDirectDebitTokenizationService tokenizationService

    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private PaymentTransactionModel transaction = Mock()

    private final String storedPaymentMethodId = "stored-method-id"
    private final String paymentInfoCode = "payment-info-code"
    private final String pspReference = "psp-reference"

    void setup() {
        tokenizationService = new AdyenSepaDirectDebitTokenizationService(transactionService, tokenizationFacade, paymentInfoService)

        paymentInfo.getCode() >> paymentInfoCode
        paymentInfo.isSaved() >> false
    }

    def "tokenizePaymentInfo should create a tokenization transaction and update the payment info"() {
        given:
        def expectedTokenizationResult = TokenizationResult.of(storedPaymentMethodId, pspReference, 'success')

        when:
        tokenizationService.tokenizePaymentInfo(paymentInfo)

        then:
        1 * transactionService.initiateTokenizationTransaction(paymentInfo) >> transaction
        1 * tokenizationFacade.tokenize(transaction) >> expectedTokenizationResult
        1 * transactionService.saveTokenizationSuccess(transaction, expectedTokenizationResult)
        1 * paymentInfoService.updateAfterTokenization(paymentInfo, expectedTokenizationResult)
    }

    def "tokenizePaymentInfo should handle tokenization failure"() {
        given:
        def exception = TokenizationException.forTransaction(transaction, new RuntimeException("Tokenization failed"))

        when:
        tokenizationService.tokenizePaymentInfo(paymentInfo)

        then:
        1 * transactionService.initiateTokenizationTransaction(paymentInfo) >> transaction
        1 * tokenizationFacade.tokenize(transaction) >> { throw exception }
        1 * transactionService.saveTokenizationFailure(transaction, _)
        0 * paymentInfoService.updateAfterTokenization(_, _)

        and:
        def thrownException = thrown(TokenizationException)
        thrownException == exception
    }

    def "tokenizePaymentInfo should validate payment info is not saved before tokenization"() {
        when:
        tokenizationService.tokenizePaymentInfo(paymentInfo)

        then:
        def exception = thrown(IllegalStateException)
        exception.message.contains("already saved and must not be updated anymore")

        and:
        paymentInfo.isSaved() >> true
        0 * transactionService.initiateTokenizationTransaction(_)
        0 * tokenizationFacade.tokenize(_)
        0 * paymentInfoService.updateAfterTokenization(_, _)
    }

    def "tokenizePaymentInfo should validate payment info does not have a token before tokenization"() {
        when:
        tokenizationService.tokenizePaymentInfo(paymentInfo)

        then:
        def exception = thrown(IllegalStateException)
        exception.message.contains("already has a recurring reference")

        and:
        paymentInfo.getRecurringReference() >> 'recurring-reference'
        0 * transactionService.initiateTokenizationTransaction(_)
        0 * tokenizationFacade.tokenize(_)
        0 * paymentInfoService.updateAfterTokenization(_, _)
    }
}
