package com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.ADYEN

@UnitTest
class TokenizedSepaDirectDebitPaymentInfoFactoryUnitSpec extends JUnitPlatformSpecification {

    private ModelService modelService = Mock()

    private TokenizedSepaDirectDebitPaymentInfoFactory paymentInfoFactory

    private IntegratorModel integrator = Mock()
    private IoTCompanyModel company = Mock()

    private final String companyId = 'company-id'

    def setup() {
        paymentInfoFactory = new TokenizedSepaDirectDebitPaymentInfoFactory(modelService)

        company.getUid() >> companyId
        integrator.getCompany() >> company
    }

    @Test
    void 'createPaymentInfo creates payment info with the given information as unsaved info'() {
        given:
        def newPaymentInfo = TokenizedSepaDirectDebitPaymentInfoBuilder.generate().buildInstance()
        def givenPaymentInfoData = createPaymentInfoData()

        when:
        def actualPaymentInfo = paymentInfoFactory.createPaymentInfo(integrator, givenPaymentInfoData)

        then:
        1 * modelService.create(TokenizedSepaDirectDebitPaymentInfoModel.class) >> newPaymentInfo
        1 * modelService.save(newPaymentInfo)
        1 * modelService.refresh(integrator)
        actualPaymentInfo == newPaymentInfo
        verifyAll(actualPaymentInfo) {
            user == integrator
            !saved
            paymentProvider == ADYEN
            IBAN == givenPaymentInfoData.iban
            accountHolderName == givenPaymentInfoData.accountHolderName
            shopperReference == "shopper_ref_${companyId}"
        }
    }

    TokenizedSepaDirectDebitPaymentInfoData createPaymentInfoData() {
        def paymentInfoData = new TokenizedSepaDirectDebitPaymentInfoData()
        paymentInfoData.setAccountHolderName('account-holder-name')
        paymentInfoData.setIban('**********************')
        paymentInfoData.setPaymentProvider(ADYEN)
        return paymentInfoData
    }
}
