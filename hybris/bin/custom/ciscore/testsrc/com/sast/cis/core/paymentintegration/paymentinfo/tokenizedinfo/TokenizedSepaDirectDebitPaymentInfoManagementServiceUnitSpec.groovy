package com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo

import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.data.MandateDetails
import com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo.data.TokenizationDetails
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.user.UserModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import static org.assertj.core.util.DateUtil.now

@UnitTest
class TokenizedSepaDirectDebitPaymentInfoManagementServiceUnitSpec extends JUnitPlatformSpecification {

    private TokenizedSepaDirectDebitPaymentInfoRetrievalService paymentInfoRetrievalService = Mock()
    private TokenizedSepaDirectDebitPaymentInfoValidator paymentInfoValidator = Mock()
    private TokenizedSepaDirectDebitPaymentInfoFactory paymentInfoFactory = Mock()
    private ModelService modelService = Mock()

    private TokenizedSepaDirectDebitPaymentInfoManagementService paymentInfoManagementService

    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel originalInfo = Mock()

    private final String merchantId = 'merchant-id'
    private final String mandateReference = 'mandate-reference'
    private final String recurringReference = 'recurring-reference'
    private final String pspTokenizationReference = 'psp-reference'

    private final Date dateOfSignature = now()

    def setup() {
        paymentInfoManagementService = new TokenizedSepaDirectDebitPaymentInfoManagementService(
                paymentInfoRetrievalService,
                paymentInfoValidator,
                paymentInfoFactory,
                modelService
        )

        paymentInfo.getDuplicate() >> false
        originalInfo.getDuplicate() >> false
    }

    @Test
    void 'assignMerchantId updates merchant ID on the given payment info'() {
        when:
        paymentInfoManagementService.assignMerchantId(paymentInfo, merchantId)

        then:
        1 * paymentInfoValidator.validateForMerchantIdUpdate(paymentInfo, merchantId)
        1 * paymentInfo.setPgwMerchantId(merchantId)
        1 * modelService.save(paymentInfo)
    }

    @Test
    void 'assignMerchantId updates merchant ID on the given payment info and original info'() {
        when:
        paymentInfoManagementService.assignMerchantId(paymentInfo, merchantId)

        then:
        paymentInfo.getDuplicate() >> true
        paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(paymentInfo) >> originalInfo

        and:
        1 * paymentInfoValidator.validateForMerchantIdUpdate(paymentInfo, merchantId)
        1 * paymentInfoValidator.validateForMerchantIdUpdate(originalInfo, merchantId)
        1 * paymentInfo.setPgwMerchantId(merchantId)
        1 * originalInfo.setPgwMerchantId(merchantId)
        1 * modelService.save(paymentInfo)
        1 * modelService.save(originalInfo)
    }

    @Test
    void 'assignMandateReference updates merchant reference on the given payment info'() {
        when:
        paymentInfoManagementService.assignMandateReference(paymentInfo, mandateReference)

        then:
        1 * paymentInfoValidator.validateForMandateReferenceUpdate(paymentInfo, mandateReference)
        1 * paymentInfo.setMandateReference(mandateReference)
        1 * modelService.save(paymentInfo)
    }

    @Test
    void 'assignMerchantReference updates merchant reference on the given payment info and original info'() {
        when:
        paymentInfoManagementService.assignMandateReference(paymentInfo, mandateReference)

        then:
        paymentInfo.getDuplicate() >> true
        paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(paymentInfo) >> originalInfo

        and:
        1 * paymentInfoValidator.validateForMandateReferenceUpdate(paymentInfo, mandateReference)
        1 * paymentInfoValidator.validateForMandateReferenceUpdate(originalInfo, mandateReference)
        1 * paymentInfo.setMandateReference(mandateReference)
        1 * originalInfo.setMandateReference(mandateReference)
        1 * modelService.save(paymentInfo)
        1 * modelService.save(originalInfo)
    }

    @Test
    def "updateAfterTokenization should update tokenization details on the given payment info"() {
        given:
        def tokenizationDetails = TokenizationDetails.of(recurringReference, pspTokenizationReference)

        when:
        paymentInfoManagementService.updateAfterTokenization(paymentInfo, tokenizationDetails)

        then:
        1 * paymentInfoValidator.validateForTokenizationDetailsUpdate(paymentInfo, tokenizationDetails)
        1 * paymentInfo.setRecurringReference(recurringReference)
        1 * paymentInfo.setPspTokenizationReference(pspTokenizationReference)
        1 * modelService.save(paymentInfo)
    }

    @Test
    def "updateAfterTokenization should update tokenization details on the given payment info and original info"() {
        given:
        def tokenizationDetails = TokenizationDetails.of(recurringReference, pspTokenizationReference)

        when:
        paymentInfoManagementService.updateAfterTokenization(paymentInfo, tokenizationDetails)

        then:
        paymentInfo.getDuplicate() >> true
        paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(paymentInfo) >> originalInfo

        and:
        1 * paymentInfoValidator.validateForTokenizationDetailsUpdate(paymentInfo, tokenizationDetails)
        1 * paymentInfoValidator.validateForTokenizationDetailsUpdate(originalInfo, tokenizationDetails)
        1 * paymentInfo.setRecurringReference(recurringReference)
        1 * originalInfo.setRecurringReference(recurringReference)
        1 * paymentInfo.setPspTokenizationReference(pspTokenizationReference)
        1 * originalInfo.setPspTokenizationReference(pspTokenizationReference)
        1 * modelService.save(paymentInfo)
        1 * modelService.save(originalInfo)
    }

    @Test
    def "updateAfterMandateActivation should update date of signature on the given payment info"() {
        given:
        def mandateDetails = MandateDetails.of(mandateReference, dateOfSignature)

        when:
        paymentInfoManagementService.updateAfterMandateActivation(paymentInfo, mandateDetails)

        then:
        1 * paymentInfoValidator.validateForMandateDetailsUpdate(paymentInfo, mandateDetails)
        1 * paymentInfo.setDateOfSignature(dateOfSignature)
        0 * paymentInfo.setMandateReference(mandateReference)
        1 * modelService.save(paymentInfo)
    }

    @Test
    def "updateAfterMandateActivation should update date of signature on the given payment info and original info"() {
        given:
        def mandateDetails = MandateDetails.of(mandateReference, dateOfSignature)

        when:
        paymentInfoManagementService.updateAfterMandateActivation(paymentInfo, mandateDetails)

        then:
        paymentInfo.getDuplicate() >> true
        paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(paymentInfo) >> originalInfo

        and:
        1 * paymentInfoValidator.validateForMandateDetailsUpdate(paymentInfo, mandateDetails)
        1 * paymentInfoValidator.validateForMandateDetailsUpdate(originalInfo, mandateDetails)
        1 * paymentInfo.setDateOfSignature(dateOfSignature)
        1 * originalInfo.setDateOfSignature(dateOfSignature)
        0 * paymentInfo.setMandateReference(mandateReference)
        0 * originalInfo.setMandateReference(mandateReference)
        1 * modelService.save(paymentInfo)
        1 * modelService.save(originalInfo)
    }

    def "finalizePaymentInfo should finalize the payment info"() {
        when:
        paymentInfoManagementService.finalizePaymentInfo(paymentInfo)

        then:
        1 * paymentInfoValidator.validateForFinalization(paymentInfo)
        1 * paymentInfo.setSaved(true)
        1 * modelService.save(paymentInfo)
    }

    def "finalizePaymentInfo should finalize both the payment info and original info"() {
        when:
        paymentInfoManagementService.finalizePaymentInfo(paymentInfo)

        then:
        paymentInfo.getDuplicate() >> true
        paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(paymentInfo) >> originalInfo

        and:
        1 * paymentInfoValidator.validateForFinalization(paymentInfo)
        1 * paymentInfoValidator.validateForFinalization(originalInfo)
        1 * paymentInfo.setSaved(true)
        1 * originalInfo.setSaved(true)
        1 * modelService.save(paymentInfo)
        1 * modelService.save(originalInfo)
    }

    def "removePaymentInfo should remove payment info and refresh user if associated"() {
        given:
        UserModel user = Mock()
        paymentInfo.getUser() >> user

        when:
        paymentInfoManagementService.removePaymentInfo(paymentInfo)

        then:
        1 * modelService.remove(paymentInfo)
        1 * modelService.refresh(user)
    }
}
