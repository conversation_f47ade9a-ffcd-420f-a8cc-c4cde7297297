package com.sast.cis.core.service.order;

import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.billingintegration.dto.OrderExportResult;
import com.sast.cis.core.billingintegration.events.OrderUpdateResponseEvent;
import com.sast.cis.core.dao.CisOrderDao;
import com.sast.cis.core.service.order.export.InvalidOrderExportDataException;
import com.sast.cis.core.service.order.export.OrderExportDataValidator;
import com.sast.cis.core.service.order.update.OrderRejectionResponseHandler;
import com.sast.cis.core.service.order.update.UnexpectedOrderUpdateException;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class OrderUpdateListenerUnitTest {

    @Mock
    private CisOrderDao cisOrderDao;

    @Mock
    private OrderExportDataValidator orderExportDataValidator;

    @Mock
    private OrderRejectionResponseHandler rejectedOrderExportResponseHandler;

    @InjectMocks
    private OrderUpdateListener orderUpdateListener;

    @Mock
    private OrderModel order;

    @Test
    public void givenUnexpectedResponseStatus_onEvent_throwException() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.COMPLETED)
            .build();
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));

        assertThatThrownBy(() -> orderUpdateListener.onEvent(new OrderUpdateResponseEvent(orderExportData)))
            .isInstanceOf(UnexpectedOrderUpdateException.class)
            .hasMessageStartingWith("ALERT: Order update events are expected only for rejections triggered by the platform.");

        verify(rejectedOrderExportResponseHandler, never()).handleOrder(order);
    }

    @Test
    public void givenOrderNotInProcessOfRejection_onEvent_thenDelegateToHandler() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.REJECTED)
            .build();
        when(order.getStatus()).thenReturn(OrderStatus.OPEN);
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));

        orderUpdateListener.onEvent(new OrderUpdateResponseEvent(orderExportData));

        verify(rejectedOrderExportResponseHandler).handleOrder(order);
    }

    @Test
    public void givenOrderInProcessOfRejection_whenResponseWithRejectedStatus_thenDelegateToHandler() {
        final OrderExportData orderExportData = OrderExportData.builder()
            .id("testId")
            .billingOrderId("externalTestId")
            .contractItems(List.of())
            .orderExportResult(OrderExportResult.REJECTED)
            .build();
        when(order.getStatus()).thenReturn(OrderStatus.REJECTION_IN_PROGRESS);
        when(cisOrderDao.findOrderForCode(anyString())).thenReturn(Optional.of(order));

        orderUpdateListener.onEvent(new OrderUpdateResponseEvent(orderExportData));

        verify(rejectedOrderExportResponseHandler).handleOrder(order);
    }

    @Test
    public void givenOrderWithIdDoesNotExist_onEvent_throwException() {
        final String orderId = "invalidId";
        final OrderExportData orderExportData = OrderExportData.builder().id(orderId).orderExportResult(OrderExportResult.COMPLETED)
            .build();
        when(cisOrderDao.findOrderForCode(orderId)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> orderUpdateListener.onEvent(new OrderUpdateResponseEvent(orderExportData)))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("ALERT: Order with code '%s' does not exist", orderId);
    }

    @Test
    public void givenInvalidOrderExportData_whenHandle_thenPropagateException() {
        final var orderExportData = OrderExportData.builder().build();
        final var validationException = InvalidOrderExportDataException.forOrderExportData(orderExportData, "Exception");
        doThrow(validationException).when(orderExportDataValidator).validateOrderExportData(orderExportData);

        assertThatThrownBy(() -> orderUpdateListener.onEvent(new OrderUpdateResponseEvent(orderExportData)))
            .isEqualTo(validationException);

        verify(rejectedOrderExportResponseHandler, never()).handleOrder(any(OrderModel.class));
    }
}
