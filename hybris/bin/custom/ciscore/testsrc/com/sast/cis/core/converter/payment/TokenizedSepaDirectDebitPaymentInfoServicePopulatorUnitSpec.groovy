package com.sast.cis.core.converter.payment

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.paymentintegration.paymentinfo.PaymentInfoService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.*

@UnitTest
class TokenizedSepaDirectDebitPaymentInfoServicePopulatorUnitSpec extends JUnitPlatformSpecification {

    private PaymentInfoService adyenPaymentService = Mock()
    private PaymentInfoService boschTransferInfoService = Mock()

    private TokenizedSepaDirectDebitPaymentInfoServicePopulator servicePopulator

    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoData paymentInfoData = Mock()

    def setup() {
        servicePopulator = new TokenizedSepaDirectDebitPaymentInfoServicePopulator(
                Optional.of(List.of(adyenPaymentService, boschTransferInfoService))
        )

        adyenPaymentService.getPaymentProvider() >> ADYEN
        boschTransferInfoService.getPaymentProvider() >> BOSCH_TRANSFER
    }

    @Test
    void 'Adyen service is called if given payment info has provider Adyen'() {
        when:
        servicePopulator.populate(paymentInfo, paymentInfoData)

        then:
        paymentInfo.getPaymentProvider() >> ADYEN
        1 * adyenPaymentService.populatePaymentInfoData(paymentInfo, paymentInfoData)
        0 * boschTransferInfoService.populatePaymentInfoData(paymentInfo, paymentInfoData)
    }

    @Test
    void 'BOSCH_TRANSFER service is called if given payment info has provider BOSCH_TRANSFER'() {
        when:
        servicePopulator.populate(paymentInfo, paymentInfoData)

        then:
        paymentInfo.getPaymentProvider() >> BOSCH_TRANSFER
        0 * adyenPaymentService.populatePaymentInfoData(paymentInfo, paymentInfoData)
        1 * boschTransferInfoService.populatePaymentInfoData(paymentInfo, paymentInfoData)
    }

    @Test
    void 'no service is called if given payment info has no payment provider'() {
        when:
        servicePopulator.populate(paymentInfo, paymentInfoData)

        then:
        0 * adyenPaymentService.populatePaymentInfoData(paymentInfo, paymentInfoData)
        0 * boschTransferInfoService.populatePaymentInfoData(paymentInfo, paymentInfoData)
    }

    @Test
    void 'no service is called if given payment info has a payment provider with unregistered payment info service'() {
        when:
        servicePopulator.populate(paymentInfo, paymentInfoData)

        then:
        paymentInfo.getPaymentProvider() >> ZERO
        0 * adyenPaymentService.populatePaymentInfoData(paymentInfo, paymentInfoData)
        0 * boschTransferInfoService.populatePaymentInfoData(paymentInfo, paymentInfoData)
    }
}
