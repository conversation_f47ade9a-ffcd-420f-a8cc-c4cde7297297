package com.sast.cis.core.converter.payment

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.service.customer.integrator.IntegratorService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.PK
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static org.assertj.core.util.DateUtil.now

@UnitTest
class TokenizedSepaDirectDebitPaymentInfoPopulatorUnitSpec extends JUnitPlatformSpecification {
    private IntegratorService integratorService = Mock()

    private TokenizedSepaDirectDebitPaymentInfoPopulator paymentInfoPopulator

    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = Mock()
    private IntegratorModel customer = Mock()

    private final String paymentInfoCode = 'payment-info-code'
    private final String accountHolderName = 'account-holder-name'
    private final String iban = 'iban'
    private final String mandateReference = 'mandate-reference'
    private final String shopperReference = 'shopper-reference'
    private final String recurringReference = 'recurring-reference'
    private final Date mandateDateOfSignature = now()


    def setup() {
        paymentInfoPopulator = new TokenizedSepaDirectDebitPaymentInfoPopulator()
        paymentInfoPopulator.setIntegratorService(integratorService)

        paymentInfo.getPk() >> PK.fromLong(1)
        paymentInfo.getCode() >> paymentInfoCode
        paymentInfo.getPaymentProvider() >> ADYEN
        paymentInfo.getAccountHolderName() >> accountHolderName
        paymentInfo.getIBAN() >> iban
        paymentInfo.getMandateReference() >> mandateReference
        paymentInfo.getDateOfSignature() >> mandateDateOfSignature
        paymentInfo.getShopperReference() >> shopperReference
        paymentInfo.getRecurringReference() >> recurringReference
        paymentInfo.isCompanyScope() >> true
        paymentInfo.isSaved() >> true
        paymentInfo.getUser() >> customer

        integratorService.getCurrentIntegrator() >> customer
        customer.getDefaultPaymentInfo() >> paymentInfo
    }

    @Test
    void 'populate sets all relevant data from given info'() {
        given:
        def paymentInfoData = new TokenizedSepaDirectDebitPaymentInfoData()

        when:
        paymentInfoPopulator.populate(paymentInfo, paymentInfoData)

        then:
        verifyAll(paymentInfoData) {
            saved
            defaultPaymentInfo
            paymentProvider == ADYEN
            paymentMethod == SEPA_DIRECTDEBIT
            accountHolderName == accountHolderName
            iban == iban
            dateOfSignature == dateOfSignature
            mandateReference == mandateReference
            shopperReference == shopperReference
            recurringReference == recurringReference
            companyScope == companyScope
        }
    }

}
