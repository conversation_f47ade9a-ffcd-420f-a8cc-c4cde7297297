package com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo

import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.ADYEN

@UnitTest
class TokenizedSepaDirectDebitPaymentInfoRetrievalServiceUnitSpec extends JUnitPlatformSpecification {

    private IntegratorPaymentInfoService integratorPaymentInfoService = Mock()

    private TokenizedSepaDirectDebitPaymentInfoRetrievalService paymentInfoRetrievalService

    private TokenizedSepaDirectDebitPaymentInfoModel firstStoredInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel secondStoredInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfoWithOtherMerchant = Mock()

    private IntegratorModel integrator = Mock()
    private AbstractOrderModel cart = Mock()

    private final String merchantId = 'merchant-id'

    def setup() {
        paymentInfoRetrievalService = new TokenizedSepaDirectDebitPaymentInfoRetrievalService(integratorPaymentInfoService)

        firstStoredInfo.getUser() >> integrator
        firstStoredInfo.getPgwMerchantId() >> merchantId

        secondStoredInfo.getUser() >> integrator
        secondStoredInfo.getPgwMerchantId() >> merchantId

        paymentInfoWithOtherMerchant.getPgwMerchantId() >> "other-merchant-id"
        paymentInfoWithOtherMerchant.getUser() >> integrator
    }

    @Test
    void 'getPaymentInfos retrieves infos for the given integrator'() {
        when:
        def actualPaymentInfos = paymentInfoRetrievalService.getPaymentInfos(integrator, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getPaymentInfos(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo] as Set)
        actualPaymentInfos == [firstStoredInfo, secondStoredInfo] as Set
    }

    @Test
    void 'given payment info with other merchant when getPaymentInfos then exclude from result'() {
        when:
        def actualPaymentInfos = paymentInfoRetrievalService.getPaymentInfos(integrator, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getPaymentInfos(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> ([firstStoredInfo, paymentInfoWithOtherMerchant] as Set)
        actualPaymentInfos == [firstStoredInfo] as Set
    }

    @Test
    void 'getDefaultPaymentInfo retrieves matching default payment info'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getDefaultPaymentInfo(integrator, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(secondStoredInfo)
        actualPaymentInfo == Optional.of(secondStoredInfo)
    }

    @Test
    void 'given user has no default payment info when getDefaultPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getDefaultPaymentInfo(integrator, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'given user has no default payment info with matching merchant id when getDefaultPaymentInfo then return empty1'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getDefaultPaymentInfo(integrator, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(paymentInfoWithOtherMerchant)
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getCartPaymentInfo retrieves matching order payment info'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getCartPaymentInfo(cart, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(firstStoredInfo)
        actualPaymentInfo == Optional.of(firstStoredInfo)
    }

    @Test
    void 'given user has no default payment info when getCartPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getCartPaymentInfo(cart, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'given user has no default payment info with matching merchant id when getDefaultPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoRetrievalService.getCartPaymentInfo(cart, ADYEN, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(paymentInfoWithOtherMerchant)
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getOriginalPaymentInfoOrThrow retrieves the original info'() {
        given:
        def givenInfo = TokenizedSepaDirectDebitPaymentInfoBuilder.generate().withDuplicate(true).buildInstance()
        def originalInfo = TokenizedSepaDirectDebitPaymentInfoBuilder.generate().buildInstance()

        when:
        def result = paymentInfoRetrievalService.getOriginalPaymentInfoOrThrow(givenInfo)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(givenInfo, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(originalInfo)
        result == originalInfo
    }
}
