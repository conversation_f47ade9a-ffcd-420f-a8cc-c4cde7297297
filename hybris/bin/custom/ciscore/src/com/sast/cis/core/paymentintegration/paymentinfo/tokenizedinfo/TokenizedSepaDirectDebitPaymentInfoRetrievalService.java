package com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo;

import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Component
public class TokenizedSepaDirectDebitPaymentInfoRetrievalService {

    private final IntegratorPaymentInfoService integratorPaymentInfoService;

    /**
     * Retrieves matching stored payment infos for the given integrator.
     * <br/>
     * A Payment Info is considered stored and matching only if all the following criteria are met:
     * <ol>
     *     <li>The info user is the given integrator</li>
     *     <li>The type is TokenizedSepaDirectDebitPaymentInfoModel</li>
     *     <li>The paymentProvider is {@code provider}</li>
     *     <li>The info is marked as saved</li>
     *     <li>The info is not marked as duplicate</li>
     *     <li>Its pgwMerchantId is identical to the given merchantId</li>
     * </ol>
     *
     * @param integrator User for which payment infos are searched
     * @param provider  The payment provider for which payment infos are searched
     * @param merchantId The merchant ID all payment infos must match
     * @return All matching stored payment infos for the given integrator
     */
    public Set<TokenizedSepaDirectDebitPaymentInfoModel> getPaymentInfos(
        @NonNull final IntegratorModel integrator,
        @NonNull final PaymentProvider provider,
        @NonNull final String merchantId) {

        return integratorPaymentInfoService.getPaymentInfos(integrator, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .stream()
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()))
            .collect(Collectors.toSet());
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getDefaultPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final PaymentProvider provider,
        @NonNull final String merchantId) {

        return integratorPaymentInfoService.getDefaultPaymentInfo(integrator, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()));
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getCartPaymentInfo(
        @NonNull final AbstractOrderModel cart,
        @NonNull final PaymentProvider provider,
        @NonNull final String merchantId) {

        return integratorPaymentInfoService.getCartPaymentInfo(cart, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()));
    }

    public TokenizedSepaDirectDebitPaymentInfoModel getOriginalPaymentInfoOrThrow(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {

        return integratorPaymentInfoService
            .getNonDuplicateOriginalInfo(paymentInfo, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .orElseThrow(() ->
                new IllegalStateException("Given duplicate info %s does not have an original info".formatted(paymentInfo.getCode()))
            );
    }
}
