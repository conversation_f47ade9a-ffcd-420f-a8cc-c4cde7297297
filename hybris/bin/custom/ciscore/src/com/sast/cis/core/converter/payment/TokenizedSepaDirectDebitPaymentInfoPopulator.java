package com.sast.cis.core.converter.payment;

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import org.springframework.stereotype.Component;

import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT;

@Component
public class TokenizedSepaDirectDebitPaymentInfoPopulator
    extends AbstractPaymentInfoPopulator<TokenizedSepaDirectDebitPaymentInfoModel, TokenizedSepaDirectDebitPaymentInfoData> {

    @Override
    public void populate(final TokenizedSepaDirectDebitPaymentInfoModel source, final TokenizedSepaDirectDebitPaymentInfoData target) {
        super.populate(source, target);

        target.setPaymentMethod(SEPA_DIRECTDEBIT);

        target.setAccountHolderName(source.getAccountHolderName());
        target.setIban(source.getIBAN());

        target.setDateOfSignature(source.getDateOfSignature());
        target.setMandateReference(source.getMandateReference());

        target.setShopperReference(source.getShopperReference());
        target.setRecurringReference(source.getRecurringReference());
    }

}
