package com.sast.cis.core.paymentintegration.exception;

import com.sast.cis.core.enums.PaymentProvider;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import lombok.NonNull;

public class PaymentInfoNotSupportedByProviderException extends RuntimeException {

    private PaymentInfoNotSupportedByProviderException(final String message) {
        super(message);
    }

    public static <T extends PaymentInfoModel> PaymentInfoNotSupportedByProviderException forInfoTypeAndProvider(
        @NonNull final Class<T> paymentInfoType,
        @NonNull final PaymentProvider paymentProvider) {

        return new PaymentInfoNotSupportedByProviderException(
            "Payment Info '%s' not supported by provider '%s'".formatted(paymentInfoType.getSimpleName(), paymentProvider)
        );
    }
}
