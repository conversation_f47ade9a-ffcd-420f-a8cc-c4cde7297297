package com.sast.cis.core.order.service;

import com.sast.cis.core.billingintegration.request.OrderExport;
import com.sast.cis.core.order.exception.OrderRejectionException;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.UUID;

import static de.hybris.platform.core.enums.ExportStatus.EXPORTED;
import static de.hybris.platform.core.enums.OrderStatus.REJECTED;
import static de.hybris.platform.core.enums.OrderStatus.REJECTION_IN_PROGRESS;
import static de.hybris.platform.searchservices.support.util.ObjectUtils.isNotEmpty;
import static java.util.UUID.randomUUID;

/**
 * Order rejection manager responsible for managing the rejection and re-export of orders.
 * Rejection refers to the process of having an order rejected in BRIM.
 * This differs from cancellation, which is a local operation, and does not involve BRIM.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderRejectionManager {

    private final OrderExport orderExport;
    private final OrderStatusTransitionService orderStatusTransitionService;
    private final ModelService modelService;
    private final TransactionTemplate transactionTemplate;

    /**
     * Initiates the rejection of the given order.
     * This will trigger the rejection of the order in BRIM, and set the order status to REJECTION_IN_PROGRESS.
     *
     * @param order the order to be rejected
     */
    public void initiateOrderRejection(@NonNull final OrderModel order) {
        LOG.info("Initiating rejection of order with code={}", order.getCode());
        validateForRejection(order);
        transactionTemplate.executeWithoutResult(status -> orderStatusTransitionService.transitionStatus(order, REJECTION_IN_PROGRESS));
        orderExport.rejectOrder(order);
        LOG.info("Rejection for order with code={} has been initiated.", order.getCode());
    }

    /**
     * Finalizes the rejection of the given order, by marking the order as REJECTED
     *
     * @param order the order to be re-exported
     */
    public void finalizeOrderRejection(@NonNull final OrderModel order) {
        LOG.info("Finalizing rejection of order with code={}", order.getCode());
        orderStatusTransitionService.transitionStatus(order, REJECTED);
        LOG.info("Rejection for order with code={} has been finalized.", order.getCode());
    }

    /**
     * Initiates the re-export of the given rejected order.
     *
     * @param order the order to be re-exported
     */
    public void initiateRejectedOrderReexport(@NonNull final OrderModel order) {
        LOG.info("Initiating re-export of rejected order with code={}", order.getCode());
        validateForReexport(order);
        order.setIdempotencyKey(randomUUID().toString());
        modelService.save(order);
        orderExport.exportOrder(order);
        LOG.info("Re-export for order with code={} has been initiated.", order.getCode());
    }

    /**
     * Finalizes the re-export of the given rejected order. This will update the order status.
     *
     *
     * @param order the order to be re-exported
     * @param newOrderStatus the new status of the order after re-export
     */
    public void finalizeRejectedOrderReexport(@NonNull final OrderModel order, @NonNull final OrderStatus newOrderStatus) {
        LOG.info("Finalizing re-export of rejected order with code={}", order.getCode());

        validateForFinalizingReexport(order);

        orderStatusTransitionService.transitionStatus(order, newOrderStatus);

        final boolean isReexportedSuccessful = orderStatusTransitionService.isSuccessStatus(newOrderStatus);
        if (!isReexportedSuccessful) {
            throw OrderRejectionException.forOrder(
                order.getCode(),
                "ALERT: Re-export of order has failed. Check current status and re-export if required"
            );
        }

        order.setOrderRecreated(false);
        modelService.save(order);

        LOG.info("Re-export for order with code={} has been finalized.", order.getCode());
    }

    /**
     * Evaluates if the given order is valid for re-export.
     *
     * @param order the order to be validated for re-export
     * @return true if the order is valid for re-export, false otherwise
     */
    public boolean isRejectedOrderValidForReexport(@NonNull final OrderModel order) {
        LOG.info("Evaluate if rejected order with code={} is valid for re-export.", order.getCode());
        return isValidForReexport(order);
    }

    private void validateForRejection(final OrderModel order) {
        final boolean isExported = EXPORTED.equals(order.getExportStatus());
        if (!isExported) {
            throw OrderRejectionException.forOrder(order.getCode(), "Order has not been exported to BRIM.");
        }

        final boolean canBeRejected = orderStatusTransitionService.canOrderBeTransitionedTo(order, REJECTION_IN_PROGRESS);
        if (!canBeRejected) {
            throw OrderRejectionException.forOrder(
                order.getCode(), "Orders with status '%s' cannot be rejected.".formatted(order.getStatus())
            );
        }
    }

    private void validateForReexport(final OrderModel order) {
        final boolean validForReexport = isValidForReexport(order);
        if (!validForReexport) {
            throw OrderRejectionException.forOrder(order.getCode(), "Order is not valid for re-export.");
        }
    }

    private boolean isValidForReexport(final OrderModel order) {
        final boolean isRejected = REJECTED.equals(order.getStatus());
        if (!isRejected) {
            LOG.info("Order has status '{}'. It is not valid for re-export.", order.getStatus());
            return false;
        }
        final boolean hasEntries = isNotEmpty(order.getEntries());
        if (!hasEntries) {
            LOG.info("Order with code='{}' does not have any entries. It is not valid for re-export.", order.getCode());
            return false;
        }
        return true;
    }

    private void validateForFinalizingReexport(final OrderModel order) {
        final boolean isOrderReexportedAfterRejection = REJECTED.equals(order.getStatus()) && order.isOrderRecreated();
        if (!isOrderReexportedAfterRejection) {
            throw OrderRejectionException.forOrder(
                order.getCode(),
                "ALERT: Order is not in the process of being recreated after rejection"
            );
        }
    }
}
