package com.sast.cis.core.paymentintegration.paymentinfo.tokenizedinfo;

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel._TYPECODE;
import static com.sast.cis.core.util.Base58UUIDCodeGenerator.generateCode;

@RequiredArgsConstructor
@Slf4j
@Component
class TokenizedSepaDirectDebitPaymentInfoFactory {

    private final ModelService modelService;

    /**
     * Create a Payment Info.
     * <br/>
     * The payment info is marked as unsaved initially since it cannot be used until the tokenization is completed.
     * <br/>
     *
     * @param integrator      User for whom the payment info is created
     * @param paymentInfoData the data to be used for creating the payment info
     * @return a new instance of TokenizedSepaDirectDebitPaymentInfoModel
     */
    public TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final TokenizedSepaDirectDebitPaymentInfoData paymentInfoData) {

        LOG.info("Create new TokenizedSepaDirectDebitPaymentInfo for integrator: {}", integrator.getUid());

        final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = modelService.create(TokenizedSepaDirectDebitPaymentInfoModel.class);
        paymentInfo.setCode(generateCode(_TYPECODE));
        paymentInfo.setPaymentProvider(paymentInfoData.getPaymentProvider());
        paymentInfo.setUser(integrator);

        paymentInfo.setIBAN(paymentInfoData.getIban());
        paymentInfo.setAccountHolderName(paymentInfoData.getAccountHolderName());
        paymentInfo.setShopperReference(generateShopperReference(integrator));

        paymentInfo.setCompanyScope(true);
        paymentInfo.setSaved(false);

        modelService.save(paymentInfo);
        modelService.refresh(integrator);

        LOG.info("Created new TokenizedSepaDirectDebitPaymentInfo with code: {}", paymentInfo.getCode());
        return paymentInfo;
    }

    private String generateShopperReference(@NonNull final IntegratorModel integrator) {
        final IoTCompanyModel company = integrator.getCompany();
        return String.format("shopper_ref_%s", company.getUid());
    }
}
