package com.sast.cis.core.converter.payment;

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import com.sast.cis.core.paymentintegration.paymentinfo.PaymentInfoService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class TokenizedSepaDirectDebitPaymentInfoServicePopulator
    extends AbstractPaymentInfoServicePopulator<TokenizedSepaDirectDebitPaymentInfoModel, TokenizedSepaDirectDebitPaymentInfoData> {

    protected TokenizedSepaDirectDebitPaymentInfoServicePopulator(
        final Optional<List<PaymentInfoService<TokenizedSepaDirectDebitPaymentInfoData, TokenizedSepaDirectDebitPaymentInfoModel>>> paymentInfoServices) {
        super(paymentInfoServices);
    }
}
