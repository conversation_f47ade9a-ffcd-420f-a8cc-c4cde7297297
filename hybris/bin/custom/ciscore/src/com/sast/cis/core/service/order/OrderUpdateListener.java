package com.sast.cis.core.service.order;

import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.billingintegration.dto.OrderExportResult;
import com.sast.cis.core.billingintegration.events.OrderUpdateResponseEvent;
import com.sast.cis.core.dao.CisOrderDao;
import com.sast.cis.core.service.order.export.OrderExportDataValidator;
import com.sast.cis.core.service.order.update.OrderRejectionResponseHandler;
import com.sast.cis.core.service.order.update.UnexpectedOrderUpdateException;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.event.impl.AbstractEventListener;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderUpdateListener extends AbstractEventListener<OrderUpdateResponseEvent> {

    private final CisOrderDao cisOrderDao;
    private final OrderExportDataValidator orderExportDataValidator;
    private final OrderRejectionResponseHandler defaultOrderRejectionResponseHandler;

    @Override
    @Transactional
    public void onEvent(@NonNull final OrderUpdateResponseEvent orderResponseEvent) {
        LOG.info("Received order update response event {}", orderResponseEvent);

        final OrderExportData orderExportData = orderResponseEvent.getOrderExportData();
        orderExportDataValidator.validateOrderExportData(orderExportData);

        final OrderModel order = findOrderForCodeOrThrow(orderExportData.getId());

        if (isPlatformTriggeredRejection(order, orderExportData)) {
            LOG.info("Order with code '{}' was REJECTED by platform", order.getCode());
            defaultOrderRejectionResponseHandler.handleOrder(order);
        } else {
            throw UnexpectedOrderUpdateException.forOrderExportData(
                orderExportData,
                "ALERT: Order update events are expected only for rejections triggered by the platform."
            );
        }
    }

    private OrderModel findOrderForCodeOrThrow(final String orderCode) {
        return cisOrderDao.findOrderForCode(orderCode)
            .orElseThrow(() -> new IllegalStateException("ALERT: Order with code '%s' does not exist".formatted(orderCode)));
    }

    private boolean isPlatformTriggeredRejection(final OrderModel order, final OrderExportData orderExportData) {
        final OrderExportResult orderExportResult = orderExportData.getOrderExportResult();
        final boolean isRejectedOrderResponse = OrderExportResult.REJECTED.equals(orderExportResult);
        if (!isRejectedOrderResponse) {
            LOG.info("Update response for order with code '{}' was not REJECTED.", order.getCode());
            return false;
        }
        final OrderStatus status = order.getStatus();
        final boolean isOrderPendingRejection = OrderStatus.REJECTION_IN_PROGRESS.equals(status);
        if (!isOrderPendingRejection) {
            LOG.warn("ALERT: Order with code '{}' and current status '{}' is not in REJECTION_IN_PROGRESS status.", order.getCode(), status);
        }

        return true;
    }
}
