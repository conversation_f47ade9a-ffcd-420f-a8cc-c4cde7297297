package com.sast.cis.core.paymentintegration.paymentinfo;

import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.NonNull;

import static com.google.common.base.Preconditions.checkState;

public class PaymentInfoPreconditions {

    private PaymentInfoPreconditions() {
    }

    public static <T extends PaymentInfoModel> T hasInfoType(
        @NonNull final AbstractOrderModel order,
        @NonNull final Class<T> infoClazz) {

        return assertPaymentInfoType(
            order.getPaymentInfo(),
            infoClazz,
            "Payment info attached to order %s is not of expected type %s",
            order.getCode()
        );
    }

    public static <T extends PaymentInfoModel> T hasInfoType(
        @NonNull final PaymentTransactionModel paymentTransaction,
        @NonNull final Class<T> infoClazz) {

        return assertPaymentInfoType(
            paymentTransaction.getInfo(),
            infoClazz,
            "Payment info attached to transaction %s is not of expected type %s",
            paymentTransaction.getCode()
        );
    }

    private static <T extends PaymentInfoModel> T assertPaymentInfoType(
        final PaymentInfoModel paymentInfo,
        final Class<T> expectedType,
        final String errorMessageFormat,
        final String contextId) {

        checkState(
            expectedType.isInstance(paymentInfo),
            errorMessageFormat,
            contextId,
            expectedType.getSimpleName()
        );
        return expectedType.cast(paymentInfo);
    }
}

