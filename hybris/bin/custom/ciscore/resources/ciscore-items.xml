<?xml version="1.0" encoding="ISO-8859-1"?>
<items xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:noNamespaceSchemaLocation="items.xsd">

    <!-- NOTE: typecodes for this extension should start at 20100 -->

    <collectiontypes>
        <collectiontype code="PermissionList" elementtype="Permission" autocreate="true" type="list"/>
        <collectiontype code="SignatureList" elementtype="ApkSignature" autocreate="true" type="list"/>
        <collectiontype code="DeviceCapabilitiesSet" elementtype="DeviceCapability" autocreate="true" type="set"/>
        <collectiontype code="PriceDraftSet" elementtype="PriceDraft" autocreate="true" type="set"/>
        <collectiontype code="LicenseTypeSet" elementtype="LicenseType" autocreate="true" type="set"/>
        <collectiontype code="PaymentProviderSet" elementtype="PaymentProvider" autocreate="true" type="set"/>
        <collectiontype code="PaymentMethodTypeSet" elementtype="PaymentMethodType" autocreate="true" type="set"/>
        <collectiontype elementtype="BaseSite" code="BaseSiteCollection" autocreate="true"/>
        <collectiontype code="CountryIsoCodeSet" elementtype="java.lang.String" autocreate="true" type="set"/>
        <collectiontype code="EulaUrlSet" elementtype="java.lang.String" autocreate="true" type="set"/>
    </collectiontypes>

    <enumtypes>
        <enumtype code="PaymentTransactionType" autocreate="false" generate="true">
            <value code="SUBMIT"/>
            <value code="CREATE_PAYMENT_INTENT"/>
            <value code="PLATFORM_CAPTURE"/>
            <value code="DEVELOPER_CAPTURE"/>
            <value code="TRANSFER"/>
            <value code="PLATFORM_TRANSFER"/>
            <value code="DEVELOPER_TRANSFER"/>
            <value code="PSP_FEE"/>
            <value code="PLATFORM_PAYOUT"/>
            <value code="DEVELOPER_PAYOUT"/>
            <value code="PENDING_PAYMENT"/>
            <value code="PARTIAL_RECONCILIATION"/>
            <value code="FULL_RECONCILIATION"/>
            <value code="INVOICE"/>
            <value code="CHECKOUT"/>
            <value code="RETURN"/>
            <value code="CHARGEBACK"/>
            <value code="CHARGEBACK_REVERSAL"/>
            <value code="TOKENIZATION"/>
        </enumtype>

        <enumtype code="PayoutStatus" autocreate="true" generate="true">
            <value code="CREATED"/>
            <value code="UPDATED"/>
            <value code="PAID"/>
            <value code="CANCELED"/>
            <value code="FAILED"/>
        </enumtype>

        <enumtype code="AppIntegrationStatus" autocreate="true" generate="true">
            <value code="ENABLED"/>
            <value code="DISABLED"/>
        </enumtype>

        <enumtype code="AppIntegrationType" autocreate="true" generate="true">
            <value code="STANDARD"/>
            <value code="CUSTOM"/>
        </enumtype>

        <enumtype code="StandardAppIntegrationType" autocreate="true" generate="true">
            <value code="GATEWAY"/>
            <value code="STANDARD"/>
        </enumtype>

        <enumtype code="ProductDataOption" autocreate="true" generate="true">
            <value code="CHANGELOGS"/>
            <value code="PRICE"/>
            <value code="GALLERY"/>
            <value code="BASIC"/>
            <value code="SUMMARY"/>
            <value code="DESCRIPTION"/>
            <value code="CATEGORIES"/>
            <value code="PROMOTIONS"/>
            <value code="REVIEW"/>
            <value code="PDFS"/>
            <value code="VARIANT_FULL"/>
            <value code="URL"/>
            <value code="INSTALLS"/>
            <value code="ACQUISITION_COUNT"/>
            <value code="LICENSES"/>
            <value code="LICENSES_WITHOUT_PRICES"/>
            <value code="SPECIAL_OFFER"/>
        </enumtype>

        <enumtype code="LicenseType" autocreate="true" generate="true" dynamic="true">
            <value code="FULL"/>
            <value code="EVALUATION"/>
            <value code="SUBSCRIPTION"/>
            <value code="TOOL"/>
        </enumtype>

        <enumtype code="LicenseAvailabilityStatus" autocreate="true" generate="true" dynamic="true">
            <value code="PUBLISHED"/>
            <value code="UNPUBLISHED"/>
        </enumtype>

        <enumtype code="BillingSystemStatus" autocreate="true" generate="true" dynamic="true">
            <value code="NEW"/>
            <value code="CREATE_PENDING"/>
            <value code="REJECTED"/>
            <value code="NOT_READY_FOR_EXPORT"/>
            <value code="UPDATE_PENDING"/>
            <value code="CANCEL_PENDING"/>
            <value code="IN_SYNC"/>
            <!--
                Applies to contracts that were cancelled before being activated in BRIM.
                After a future dated contract is cancelled, its parent order will be 're-created' in BRIM.
                This status is used to prevent the cancelled contract from being re-exported to BRIM.
            -->
            <value code="CANCELLED_BEFORE_EXPORT"/>
        </enumtype>

        <enumtype code="GroupPriceUpdateStatus" autocreate="true" generate="true" dynamic="true">
            <value code="NEW"/>
            <value code="DRAFT"/>
            <value code="UPDATE_PENDING"/>
            <value code="SYNC_PENDING"/>
            <value code="IN_SYNC"/>
        </enumtype>

        <enumtype code="SignatureVersion" autocreate="true" generate="true">
            <value code="V1"/>
            <value code="V2"/>
        </enumtype>

        <enumtype code="PaymentProvider" autocreate="true" generate="true"/>

        <enumtype code="OrderStatus" autocreate="false" generate="true" dynamic="true">
            <value code="WAIT_FOR_INVOICE"/>
            <value code="AWAITING_LICENSE_ACTIVATION"/>
            <value code="REJECTED"/>
            <value code="REJECTION_IN_PROGRESS"/>
            <value code="OPEN"/>
            <value code="ERROR"/> <!-- ERROR status can be returned by BRIM -->
        </enumtype>

        <enumtype code="PaymentStatus" autocreate="false" generate="true" dynamic="true">
            <value code="EXEMPT"/>
            <value code="FAILED"/>
            <value code="PENDING"/>
            <value code="OVERDUE"/>
            <value code="PAID_RECURRING">
                <description>
                    Applicable to Subscription orders only.
                    Used to reflect that the order's first contract period has been paid for, but that subsequent payments are expected for
                    the subscription prolongations
                </description>
            </value>
            <value code="REFUNDED"/>
            <value code="PARTIALLY_REFUNDED"/>
        </enumtype>

        <enumtype code="InvoiceStatus" autocreate="true" generate="true">
            <value code="EXEMPT"/>
            <value code="INITIAL"/>
            <value code="PAID"/>
            <value code="PAYMENT_FAILED"/>
            <value code="PENDING"/>
            <value code="OVERDUE"/>
            <value code="ACTIVATION_PENDING"/>
            <value code="REVERSED">
                <description>A reversal document has been issued for the invoice</description>
            </value>
            <value code="REFUNDED">
                <description>A credit note refunding the full invoice amount has been issued</description>
            </value>
            <value code="PARTIALLY_REFUNDED">
                <description>A credit note refunding part of the invoice amount has been issued</description>
            </value>
            <value code="NOTPAID">
                <description>Used only for Order payment status migration</description>
            </value>
        </enumtype>

        <enumtype code="CategoryPageOption" autocreate="true" generate="true">
            <value code="BASIC"/>
            <value code="REVIEW"/>
        </enumtype>

        <enumtype code="DevconPage" autocreate="true" generate="true">
            <value code="APP_VERSION_OVERVIEW"/>
            <value code="APP_VERSION_METADATA"/>
            <value code="CRASH_REPORTS"/>
            <value code="COUNTRIES_AND_PRICES"/>
            <value code="STORE_CONTENT"/>
            <value code="LANDING"/>
            <value code="PAYMENT_SERVICES"/>
            <value code="ORDER_MANAGEMENT"/>
            <value code="APP_AVAILABILITY"/>
            <value code="PRICING"/>
        </enumtype>

        <enumtype code="ArticleApprovalStatus" autocreate="false" generate="true">
            <value code="draft"/>
        </enumtype>

        <enumtype code="CreditCardType" autocreate="false" generate="true">
            <value code="discover"/>
            <value code="jcb"/>
            <value code="unionpay"/>
        </enumtype>

        <enumtype code="StripeConnectAccountStatus" autocreate="true" generate="true">
            <value code="IN_PROGRESS"/>
            <value code="COMPLETED"/>
            <value code="INVALIDATED"/>
        </enumtype>

        <enumtype code="Feature">
            <value code="FEATURE_CRASHREPORT"/>
            <value code="FEATURE_GTM_ENABLED"/>
            <value code="FEATURE_ALLOW_SELF_PURCHASE"/>
            <value code="FEATURE_ENABLE_DPG_CC"/>
            <value code="FEATURE_SUBSCRIPTION_PRICE_UPDATES"/>
            <value code="FEATURE_DIRECT_SALES"/>
            <value code="FEATURE_PAYMENT_UPDATES"/>
            <value code="FEATURE_CONTRACT_CONFIG_UPDATES"/>
            <value code="FEATURE_EMAIL_CLIENT_ENABLED"/>
            <value code="FEATURE_ENFORCE_EULA_ACCEPTANCE"/>
            <value code="FEATURE_BRIM_CUSTOM_PRICE_TEXT"/>
            <value code="FEATURE_ORDER_CONFIRMATION_INFOBOX"/>
            <value code="FEATURE_CREATE_IDW_PRICES_AS_GROUP_PRICES"/>
            <value code="FEATURE_REJECT_PRICES_WITH_UNSUPPORTED_CURRENCIES"/>
            <value code="FEATURE_ENABLE_IMPROVED_NAVIGATION"/>
            <value code="FEATURE_ENABLE_COMPANY_OPERATIONAL_STATES"/>
            <value code="FEATURE_BRIM_TIME_FIELDS_IN_GROUP_PRICE_PAYLOAD"/>
            <value code="FEATURE_EMAIL_CREDIT_NOTE_ENABLED"/>
            <value code="FEATURE_NEW_INFO_FOR_APP"/>
            <value code="FEATURE_THL_MASTER_EXTRACT_JOB_TEST_MODE"/>
            <value code="FEATURE_ENABLE_SEPA_MANDATE"/>
            <value code="FEATURE_ENABLE_ORDER_DELAYED_CONFIRMATION"/>
            <value code="FEATURE_SECONDARY_EMAIL_FOR_FINANCE"/>
        </enumtype>

        <enumtype code="RejectionCode" autocreate="true" generate="true">
            <value code="REJECTED"/>
            <value code="EXPORT_CLASSIFICATION"/>
            <value code="APK_SCAN"/>
            <value code="MANUAL"/>
        </enumtype>

        <enumtype code="PjEcoStatus" autocreate="true" generate="true" deprecatedSince="release_61">
            <value code="PENDING"/>
            <value code="APPROVED"/>
            <value code="DENIED"/>
        </enumtype>

        <enumtype code="NavigationItemType" autocreate="true" generate="true">
            <value code="GLOBAL"/>
            <value code="DEVCON"/>
            <value code="STORE"/>
        </enumtype>

        <enumtype code="NavigationItemGroup" autocreate="true" generate="true">
            <value code="FOOTER"/>
            <value code="HEADER"/>
            <value code="HELP"/>
            <value code="MARKETING_HEADER"/>
            <value code="SIDEBAR"/>
            <value code="PRODUCT_CATEGORIES"/>
            <value code="HOME_SWITCHER"/>
            <value code="COMPONENTS"/>
        </enumtype>

        <enumtype code="CompanyApprovalStatus" autocreate="true" generate="true">
            <value code="AWAITING_ACTIVATION"/>
            <value code="APPROVED_COMMERCIAL"/>
            <value code="APPROVED_NON_COMMERCIAL"/>
            <value code="UNAPPROVED"/>
        </enumtype>

        <enumtype code="AppVideoType" autocreate="true" generate="true">
            <value code="YOUTUBE"/>
        </enumtype>

        <enumtype code="AppVideoStatus" autocreate="true" generate="true">
            <value code="FOUND"/>
            <value code="NOT_FOUND"/>
        </enumtype>

        <enumtype code="StoreAvailabilityMode" autocreate="true" generate="true">
            <value code="PUBLIC">
                <description>App is listed in the public store. License availability is determined by buyer country.</description>
            </value>
            <value code="RESTRICTED_BUYER">
                <description>App is not listed in the public store. Store listing and license purchasability is restricted to a configured
                    set of buyer companies.
                </description>
            </value>
            <value code="RESTRICTED_BUYER_GROUP">
                <description>App is not listed in the public store. Store listing and license purchasability is restricted to a configured
                    set of buyer groups, rather than individual companies.
                </description>
            </value>
            <value code="UNAVAILABLE">
                <description>App is not listed in the public store. Product is unavailable for purchase.</description>
            </value>
        </enumtype>

        <enumtype code="PspSellerAccountStatus" autocreate="true" generate="true">
            <value code="ACTIVE"/>
            <value code="REJECTED"/>
            <value code="DISABLED"/>
            <value code="ONBOARDING"/>
        </enumtype>

        <enumtype code="EulaType" autocreate="true" generate="true">
            <value code="STANDARD"/>
            <value code="CUSTOM"/>
        </enumtype>

        <enumtype code="PaymentMethodType" autocreate="true" generate="true">
            <value code="CREDIT_CARD"/>
            <value code="INVOICE"/>
            <value code="SEPA_CREDIT"/>
            <value code="ZERO"/>
            <value code="ACH_INTERNATIONAL"/>
            <value code="INVOICE_BY_SELLER"/>
            <value code="SEPA_DIRECTDEBIT"/>
        </enumtype>

        <enumtype code="LicenseActivationStatus" autocreate="true" generate="true">
            <value code="ACTIVE"/>
            <value code="INACTIVE"/>
        </enumtype>

        <enumtype code="FollowAppSubscriptionStatus" autocreate="true" generate="true">
            <value code="SUBSCRIBED"/>
            <value code="UNSUBSCRIBED"/>
        </enumtype>

        <enumtype code="CreditNoteType" autocreate="true" generate="true">
            <value code="REFUND"/>
            <value code="PARTIAL_REFUND"/>
            <value code="REVERSAL"/>
        </enumtype>

        <enumtype code="TerminationRuleUnit" autocreate="true" generate="true">
            <value code="YEAR"/>
            <value code="MONTH"/>
            <value code="DAY"/>
        </enumtype>

        <enumtype code="SelfBillingInvoiceStatus" autocreate="true" generate="true">
            <value code="COMPLETED"/>
            <value code="REVERSED">
                <description>A reversal document has been issued for the self billing invoice</description>
            </value>
            <value code="REFUNDED">
                <description>A credit note refunding the full self billing invoice amount has been issued</description>
            </value>
            <value code="PARTIALLY_REFUNDED">
                <description>A credit note refunding part of the self billing invoice amount has been issued</description>
            </value>
        </enumtype>

        <enumtype code="StoreEnum" autocreate="true" generate="true">
            <value code="iotstore"/>
            <value code="aastore"/>
        </enumtype>

        <enumtype code="EventProcessingStatus" autocreate="true" generate="true">
            <value code="NEW"/>
            <value code="SUCCEEDED"/>
            <value code="FAILED"/>
        </enumtype>
        <enumtype code="PriceFrequency" autocreate="true" generate="true">
            <value code="YEARLY"/>
            <value code="ONE_TIME"/>
        </enumtype>
        <enumtype code="CompanyOperationalStage" autocreate="true" generate="true">
            <value code="AWAITING_DETAILS"/>
            <value code="VALIDATING"/>
            <value code="OPERATIONAL"/>
            <value code="RESTRICTED"/>
        </enumtype>
        <enumtype code="ActivationMode" autocreate="true" generate="true">
            <value code="IMMEDIATE"/>
            <value code="DELAYED"/>
        </enumtype>

        <enumtype code="PaymentInfoDraftCreationStatus" autocreate="true" generate="true">
            <value code="IN_PROGRESS"/>
            <value code="SUCCEEDED"/>
            <value code="WAITING_FOR_CONFIRMATION"/>
            <value code="FAILED"/>
            <value code="CANCELED"/>
        </enumtype>
    </enumtypes>

    <relations>
        <relation code="Company2Apps" localized="false">
            <description>This relation holds information about the company of an app</description>
            <sourceElement type="IoTCompany" qualifier="company" cardinality="one"/>
            <targetElement type="App" qualifier="apps" cardinality="many" collectiontype="set"/>
        </relation>

        <relation code="DocumentationFiles2StoreContentDraft" localized="false">
            <description>This relation holds information about the pdf files of draft app</description>
            <sourceElement type="PdfMedia" qualifier="documentationFiles" cardinality="many" collectiontype="list"/>
            <targetElement type="StoreContentDraft" qualifier="storeContentDraft" cardinality="one"/>
        </relation>

        <relation code="AppIntegrations2StoreContentDraft" localized="false">
            <description>This relation holds information about the App Integrations of the draft app</description>
            <sourceElement type="StoreContentDraft" qualifier="storeContentDraft" cardinality="one"/>
            <targetElement type="AppIntegration" qualifier="appIntegrations" cardinality="many" collectiontype="list">
                <modifiers read="true" write="true" search="false" partof="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="StoreContentDraft2Industry" localized="false">
            <description>This relation holds information about the industries of draft app</description>
            <deployment table="storedraft_to_industries" typecode="20220"/>
            <sourceElement type="StoreContentDraft" qualifier="storeContentDraft" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="Industry" cardinality="many" qualifier="industries" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="App2Industry" localized="false">
            <description>This relation holds information about the industries of an app</description>
            <deployment table="app_to_industries" typecode="20221"/>
            <sourceElement type="App" qualifier="app" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="Industry" cardinality="many" qualifier="industries" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="DocumentationFiles2App" localized="false">
            <description>This relation holds information about the pdf files of an app</description>
            <sourceElement type="PdfMedia" qualifier="documentationFiles" cardinality="many" collectiontype="list"/>
            <targetElement type="App" qualifier="app" cardinality="one"/>
        </relation>

        <relation code="AppIntegrations2App" localized="false">
            <description>This relation holds information about the App Integrations of an app</description>
            <sourceElement type="App" qualifier="app" cardinality="one"/>
            <targetElement type="AppIntegration" qualifier="appIntegrations" cardinality="many" collectiontype="list">
                <modifiers read="true" write="true" search="false" partof="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="IoTCustomer2IoTCompany" localized="false">
            <description>An IoT company can contain many customers, but a developer always belongs to one IoT company</description>
            <sourceElement type="IoTCustomer" qualifier="employees" cardinality="many" collectiontype="set"/>
            <targetElement type="IoTCompany" qualifier="company" cardinality="one">
                <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
            </targetElement>
        </relation>

        <relation code="AbstractOrder2IoTCompany" localized="false">
            <description>
                An abstract order belongs to one IoT company, but there can be several abstract orders for one IoT company
            </description>
            <sourceElement type="AbstractOrder" cardinality="many" qualifier="abstractOrders">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="IoTCompany" cardinality="one" qualifier="company">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </targetElement>
        </relation>

        <relation code="Company2ProductContainerRelation" localized="false">
            <sourceElement type="IoTCompany" cardinality="one" qualifier="company">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </sourceElement>
            <targetElement type="ProductContainer" cardinality="many" qualifier="productContainers">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="IotCompanies2PurchasedApps" localized="false">
            <deployment table="iot_comp_to_purch_apps" typecode="20117"/>
            <sourceElement type="IoTCompany" cardinality="many" qualifier="owningCompanies" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="App" cardinality="many" qualifier="purchasedApps" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="App2AppVersions" localized="false">
            <sourceElement type="App" cardinality="one" qualifier="app">
                <modifiers read="true" write="true" initial="true" optional="false"/>
            </sourceElement>
            <targetElement type="AppVersion" cardinality="many" qualifier="versions" collectiontype="set">
                <modifiers read="true" write="true" initial="false" optional="true"/>
            </targetElement>
        </relation>

        <relation code="AppLicenses2Countries" localized="false">
            <description>Countries in which an app license is available for purchase</description>
            <deployment table="applicenses_to_countries" typecode="20129"/>
            <sourceElement type="AppLicense" cardinality="many" qualifier="appLicenses" collectiontype="set"/>
            <targetElement type="Country" cardinality="many" qualifier="enabledCountries" collectiontype="set"/>
        </relation>

        <relation code="AppLicenses2UserGroups" localized="false">
            <description>A license that is available for purchase by user groups</description>
            <deployment table="applicense_to_usergroups" typecode="20268"/>
            <sourceElement type="AppLicense" cardinality="many" qualifier="appLicenses" collectiontype="set"/>
            <targetElement type="UserGroup" cardinality="many" qualifier="userGroups" collectiontype="set"/>
        </relation>

        <relation code="AppLicenseDrafts2Countries" localized="false">
            <description>Countries in which an app license shall be available for purchase</description>
            <deployment table="licensedrafts2countries" typecode="20130"/>
            <sourceElement type="AppLicenseDraft" cardinality="many" qualifier="appLicenseDrafts" collectiontype="set"/>
            <targetElement type="Country" cardinality="many" qualifier="enabledCountries" collectiontype="set"/>
        </relation>

        <relation code="Company2StripeConnectAccount" localized="false">
            <description>Each company should have one StripeConnect account</description>
            <sourceElement type="IoTCompany" cardinality="one" qualifier="company">
                <modifiers read="true" write="true" initial="true" optional="false"/>
            </sourceElement>
            <targetElement type="StripeConnectAccount" cardinality="many" qualifier="stripeConnectAccounts" collectiontype="set"/>
        </relation>

        <relation code="AppDraft2RejectionReasons" localized="false">
            <sourceElement type="AppDraft" qualifier="appDraft" cardinality="one"/>
            <targetElement type="RejectionReason" qualifier="rejectionReasons" cardinality="many" collectiontype="list">
                <modifiers partof="true"/>
            </targetElement>
        </relation>

        <relation code="AppVersionDraft2RejectionReasons" localized="false">
            <sourceElement type="AppVersionDraft" qualifier="appVersionDraft" cardinality="one"/>
            <targetElement type="RejectionReason" qualifier="rejectionReasons" cardinality="many" collectiontype="list">
                <modifiers partof="true"/>
            </targetElement>
        </relation>

        <relation code="CountriesAndPricesDraft2AppLicenseDraft" localized="false">
            <sourceElement type="CountriesAndPricesDraft" qualifier="countriesAndPricesDraft" cardinality="one"/>
            <targetElement type="AppLicenseDraft" qualifier="appLicenses" cardinality="many" collectiontype="set">
                <modifiers partof="true"/>
            </targetElement>
        </relation>

        <relation code="IoTCompany2DeveloperPayouts" localized="false">
            <sourceElement type="IoTCompany" qualifier="company" cardinality="one">
                <modifiers read="true" write="true" initial="true" optional="false"/>
            </sourceElement>
            <targetElement type="DeveloperPayout" qualifier="payouts" cardinality="many" collectiontype="set"/>
        </relation>

        <relation code="DeveloperPayout2Orders" localized="false">
            <sourceElement type="DeveloperPayout" qualifier="developerPayout" cardinality="one"/>
            <targetElement type="Order" qualifier="orders" cardinality="many"/>
        </relation>

        <relation code="StoreContentDraft2UseCase" autocreate="true" generate="true" localized="false">
            <deployment table="storedraft_to_use_cases" typecode="20223"/>
            <sourceElement type="StoreContentDraft" qualifier="storeContentDrafts" cardinality="many">
                <modifiers read="true" initial="true" write="true" search="true"/>
            </sourceElement>
            <targetElement type="UseCase" qualifier="useCases" cardinality="many" collectiontype="set" ordered="true"/>
        </relation>

        <relation code="App2UseCase" autocreate="true" generate="true" localized="false">
            <deployment table="app_to_use_cases" typecode="20224"/>
            <sourceElement type="App" qualifier="apps" cardinality="many">
                <modifiers read="true" initial="true" write="true" search="true"/>
            </sourceElement>
            <targetElement type="UseCase" qualifier="useCases" cardinality="many" collectiontype="set">
                <modifiers read="true" initial="true" write="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="Order2Invoices" localized="false" autocreate="true">
            <deployment table="order_to_invoices" typecode="20228"/>
            <sourceElement type="Order" qualifier="order" cardinality="many" collectiontype="set">
                <description>Even though a many-to-many relation is used, an invoice can be associated with one order only</description>
                <modifiers read="true" write="true" optional="false" search="true"/>
            </sourceElement>
            <targetElement type="Invoice" qualifier="invoices" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" optional="true" partof="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="Order2SelfBillingInvoices" autocreate="true" localized="false">
            <sourceElement type="Order" qualifier="order" cardinality="one">
                <modifiers read="true" write="true" optional="false" search="true"/>
            </sourceElement>
            <targetElement type="SelfBillingInvoice" qualifier="selfBillingInvoices" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" optional="true" partof="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="Invoice2InvoiceCreditNotes" autocreate="true" localized="false">
            <sourceElement type="Invoice" qualifier="originalInvoice" cardinality="one">
                <modifiers read="true" write="true" optional="false" search="true"/>
            </sourceElement>
            <targetElement type="InvoiceCreditNote" qualifier="creditNotes" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" optional="true" partof="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="Invoice2InvoiceItems" localized="false" autocreate="true">
            <sourceElement type="Invoice" qualifier="originalInvoice" cardinality="one">
                <modifiers read="true" write="true" optional="false" search="true"/>
            </sourceElement>
            <targetElement type="InvoiceItem" qualifier="invoiceItems" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" optional="true" partof="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="SelfBillingInvoice2SelfBillingInvoiceCreditNotes" autocreate="true" localized="false">
            <sourceElement type="SelfBillingInvoice" qualifier="originalSelfBillingInvoice" cardinality="one">
                <modifiers read="true" write="true" optional="false" search="true"/>
            </sourceElement>
            <targetElement type="SelfBillingInvoiceCreditNote" qualifier="creditNotes" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" optional="true" partof="true" search="true"/>
            </targetElement>
        </relation>

        <relation code="AppLicense2VolumeDiscounts" localized="false" autocreate="true">
            <sourceElement type="AppLicense" qualifier="appLicense" cardinality="one">
                <modifiers read="true" initial="true" write="true" search="true" unique="true" optional="false"/>
            </sourceElement>
            <targetElement type="VolumeDiscount" qualifier="volumeDiscounts" cardinality="many">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="AppLicenseDraft2VolumeDiscounts" localized="false" autocreate="true">
            <sourceElement type="AppLicenseDraft" qualifier="appLicenseDraft" cardinality="one">
                <modifiers read="true" initial="true" write="true" search="true" unique="true" optional="false"/>
            </sourceElement>
            <targetElement type="VolumeDiscountDraft" qualifier="volumeDiscounts" cardinality="many">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="AppDraft2PermittedBuyerCompanies" localized="false" autocreate="true">
            <deployment table="appdraft2permittedbuyers" typecode="20232"/>
            <sourceElement type="AppDraft" qualifier="privateAppDrafts" cardinality="many" collectiontype="set">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="false"/>
            </sourceElement>
            <targetElement type="IoTCompany" qualifier="permittedBuyerCompanies" cardinality="many" collectiontype="set">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="false"/>
            </targetElement>
        </relation>

        <relation code="App2PermittedBuyerCompanies" localized="false" autocreate="true">
            <deployment table="app2permittedbuyers" typecode="20233"/>
            <sourceElement type="App" qualifier="privateApps" cardinality="many" collectiontype="set">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="false"/>
            </sourceElement>
            <targetElement type="IoTCompany" qualifier="permittedBuyerCompanies" cardinality="many" collectiontype="set">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="false"/>
            </targetElement>
        </relation>

        <relation localized="false" code="AbstractOrderEntry2Subscription" autocreate="true">
            <description>The Subscription type has been deprecated and replaced by BuyerContract</description>
            <sourceElement type="AbstractOrderEntry" qualifier="orderEntry" cardinality="one"/>
            <targetElement type="Subscription" qualifier="subscriptions" cardinality="many">
                <modifiers partof="true"/>
            </targetElement>
        </relation>

        <relation localized="false" code="AbstractOrderEntry2BuyerContract" autocreate="true">
            <sourceElement type="AbstractOrderEntry" qualifier="orderEntry" cardinality="one">
                <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
            </sourceElement>
            <targetElement type="BuyerContract" qualifier="buyerContracts" cardinality="many">
                <modifiers partof="true"/>
            </targetElement>
        </relation>

        <relation code="IoTCompany2PspSellerAccount" localized="false">
            <description>Relation between IoTCompany and PspSellerAccount</description>
            <sourceElement type="IoTCompany" qualifier="company" cardinality="one"/>
            <targetElement type="PspSellerAccount" qualifier="pspSellerAccounts" cardinality="many" collectiontype="list">
                <modifiers read="true" initial="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="PrivateOfferRequest2DeveloperIoTCompany" localized="false">
            <description>Relation between PrivateOfferRequest and developer IoTCompany</description>
            <sourceElement type="PrivateOfferRequest" qualifier="privateOfferRequest" cardinality="many">
                <modifiers partof="true"/>
            </sourceElement>
            <targetElement type="IoTCompany" qualifier="developerCompany" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </targetElement>
        </relation>

        <relation code="PrivateOfferRequest2IntegratorIoTCompany" localized="false">
            <description>Relation between PrivateOfferRequest and integrator IoTCompany</description>
            <sourceElement type="PrivateOfferRequest" qualifier="privateOfferRequest" cardinality="many">
                <modifiers partof="true"/>
            </sourceElement>
            <targetElement type="IoTCompany" qualifier="integratorCompany" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </targetElement>
        </relation>

        <relation code="PrivateOfferRequest2App" localized="false">
            <description>Relation between PrivateOfferRequest and App</description>
            <sourceElement type="PrivateOfferRequest" qualifier="privateOfferRequest" cardinality="many">
                <modifiers partof="true"/>
            </sourceElement>
            <targetElement type="App" qualifier="app" cardinality="one">
                <modifiers read="true" write="true" search="true" optional="false"/>
            </targetElement>
        </relation>

        <relation code="PrivateOfferRequest2PrivateOfferItem" localized="false">
            <description>Relation between PrivateOfferRequest and PrivateOfferItem</description>
            <sourceElement type="PrivateOfferRequest" qualifier="privateOfferRequest" cardinality="one"/>
            <targetElement type="PrivateOfferRequestItem" qualifier="privateOfferRequestItems" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="false" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="App2EulaContainer" localized="false">
            <description>Country specific End User License Agreements applicable to Apps</description>
            <deployment table="app2eulacontainer" typecode="20271"/>
            <sourceElement type="App" qualifier="apps" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </sourceElement>
            <targetElement type="EulaContainer" qualifier="eulaContainers" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <relation code="EulaContainer2CountryEula" localized="false">
            <description>Country specific End User License Agreement part of EulaContainers</description>
            <sourceElement type="EulaContainer" qualifier="eulaContainer" cardinality="one"/>
            <targetElement type="CountryEula" qualifier="countryEulas" cardinality="many">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="EulaAcceptance2EulaContainer" localized="false">
            <description>
                Relation between EulaAcceptance and EulaContainers.
                Represents the eulas that are accepted as part of an Order.
            </description>
            <deployment table="eulaacc2eulacontainer" typecode="20273"/>
            <sourceElement type="EulaAcceptance" cardinality="many" collectiontype="set" navigable="false"/>
            <targetElement type="EulaContainer" qualifier="eulaContainers" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true"/>
            </targetElement>
        </relation>

        <!-- This is declared here and not in cisaacore because of some jalo ..stuff.. on CountryModel -->
        <relation code="AaDistributor2Country" localized="false">
            <description>Countries in which this distributor is active</description>
            <deployment table="aadistributor2country" typecode="26109"/>
            <sourceElement type="AaDistributorCompany" qualifier="aaDistributorCompanies" cardinality="many" collectiontype="set"/>
            <targetElement type="Country" qualifier="countries" cardinality="many" collectiontype="set"/>
        </relation>

        <relation code="NavigationItem2Attributes" localized="false">
            <description>Supplemental HTML attributes for navigation items</description>
            <sourceElement type="NavigationItem" qualifier="navigationItem" cardinality="one">
                <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
            </sourceElement>
            <targetElement type="NavigationItemAttribute" qualifier="customAttributes" cardinality="many" collectiontype="set">
                <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
            </targetElement>
        </relation>

        <relation code="Order2OpenOrderCancelBusinessProcess" localized="false">
            <sourceElement type="Order" cardinality="one" qualifier="order"/>
            <targetElement type="OpenOrderCancelBusinessProcess" cardinality="many" qualifier="openOrderProcesses" collectiontype="set">
                <modifiers initial="true"/>
            </targetElement>
        </relation>
    </relations>

    <itemtypes>
        <!-- This is declared here and not in cisaacore because of some jalo ..stuff.. on CountryModel -->
        <itemtype code="AaDistributorCompany" autocreate="true" generate="true">
            <deployment table="aadistributorcompany" typecode="26110"/>
            <attributes>
                <attribute qualifier="umpId" type="java.lang.String">
                    <description>UMP Distributor ID for AA distributor</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
                <attribute qualifier="companyName" type="java.lang.String">
                    <description>Distributor company name</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="aaExternalId" type="java.lang.String">
                    <description>ID used in AA external systems</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="distributorUmpId_idx" unique="true">
                    <key attribute="umpId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Product" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="idempotencyKey" type="java.lang.String">
                    <description>The idempotencyKey used for PJ-Eco communication</description>
                    <persistence type="property"/>
                    <modifiers optional="true"/>
                </attribute>
                <attribute qualifier="pendingProductInfo" type="PendingProductInfo">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="countryRestricted" type="boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="App" extends="Product" autocreate="true" generate="true">
            <attributes>
                <attribute qualifier="packageName" type="java.lang.String">
                    <description>The unique package name of an app</description>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
                    <persistence type="property">
                        <columntype database="mysql">
                            <value>VARCHAR(255)</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="icon" type="MediaContainer">
                    <description>The app's icon to display in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="emailAddress" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <description>Email address used for contact and support of app.</description>
                </attribute>
                <attribute qualifier="supportPhoneNumber" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Support phone number used for contact information of app.</description>
                </attribute>
                <attribute qualifier="privacyPolicyUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <description>Privacy policy URL used for contact information of app.</description>
                </attribute>
                <attribute qualifier="supportPageUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>URL for a support page or contact information of the app.</description>
                </attribute>
                <attribute qualifier="termsOfUseUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>URL for the Terms of Use of the app.</description>
                    <model>
                        <getter name="termsOfUseUrl" deprecated="true" deprecatedSince="release_74" default="true"/>
                        <setter name="termsOfUseUrl" deprecated="true" deprecatedSince="release_74" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="eula" type="Eula">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>End User License Agreement for the app.</description>
                </attribute>
                <attribute qualifier="productWebsiteUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Website of the app or the company.</description>
                </attribute>
                <attribute qualifier="enabledInStore" type="boolean">
                    <defaultvalue>true</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <description>Flag to enable/disable app in store.</description>
                    <model>
                        <getter name="enabledInStore" deprecated="true" deprecatedSince="release_61" default="true"/>
                        <setter name="enabledInStore" deprecated="true" deprecatedSince="release_61" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="masterEnabled" type="boolean">
                    <defaultvalue>true</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <description>Enable or disable app as platform administrator. Flag have precedence over storeAvailabilityMode.
                    </description>
                </attribute>
                <attribute qualifier="specifiedPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>
                        Price of the app in the currency associated with the country of the IoTCompany.
                        Deprecation Reason: Price is now part of a license
                    </description>
                    <model>
                        <getter name="specifiedPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                        <setter name="specifiedPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="subscriptionPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>
                        Price of the subscription app license in the currency associated with the country of the IoTCompany.
                        Deprecation Reason: Price is now part of a license
                    </description>
                    <model>
                        <getter name="subscriptionPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                        <setter name="subscriptionPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="latestVersion" type="AppVersion">
                    <persistence type="dynamic" attributeHandler="dynamicLatestAppVersionHandler"/>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="publishDate" type="java.util.Date">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="video" type="AppVideo">
                    <description>The app's video to display in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="storeAvailabilityMode" type="StoreAvailabilityMode">
                    <description>The app's availability in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("StoreAvailabilityMode", "UNAVAILABLE")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="newCountryAddingAllowed" type="boolean">
                    <description>Developers consent to make app available in new eco system countries automatically.</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="packageName_idx" unique="true">
                    <key attribute="packageName"/>
                    <key attribute="catalogVersion"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Eula" autocreate="true" generate="true">
            <deployment table="eula" typecode="20239"/>
            <attributes>
                <attribute qualifier="type" type="EulaType">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>STANDARD</defaultvalue>
                    <persistence type="property"/>
                    <description>Whether the S&amp;ST standard EULA or a customer EULA applies to the App</description>
                </attribute>
                <attribute qualifier="customUrl" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>The URL of a custom EULA for the App</description>
                </attribute>
                <attribute qualifier="standardEulaAppendix" type="PdfMedia">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>For standard EULAs: an appendix containing extra conditions</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CountryEula" autocreate="true" generate="true">
            <deployment table="countryeula" typecode="20269"/>
            <attributes>
                <attribute qualifier="country" type="Country">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Country the EULA applies to</description>
                </attribute>
                <attribute qualifier="eula" type="Eula">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>EULA applying to the country</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="EulaContainer" autocreate="true" generate="true">
            <deployment table="eulacontainer" typecode="20270"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>Unique business key</description>
                </attribute>
                <attribute qualifier="label" type="localized:java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Localized display name</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ThlConfigration" autocreate="true" generate="true">
            <deployment table="thlConfigration" typecode="20275"/>
            <attributes>
                <attribute qualifier="masterProductCode" type="java.lang.String">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>Master product code</description>
                </attribute>
                <attribute qualifier="thlProductCode" type="java.lang.String">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>THL product code</description>
                </attribute>
                <attribute qualifier="customerGroup" type="java.lang.String">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>Customer group</description>
                </attribute>
                <attribute qualifier="country" type="java.lang.String">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>Country</description>
                </attribute>
                <attribute qualifier="discountPercentage" type="java.lang.Double">
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <description>Discount percentage</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="StandardAppIntegration" autocreate="true" generate="true">
            <deployment table="standardappintegration" typecode="20237"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" unique="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="name" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="order" type="java.lang.Integer">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="displayName" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="integrationType" type="StandardAppIntegrationType">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="description" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="externalDescription" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabled" type="java.lang.Boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <defaultvalue>Boolean.TRUE</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AppIntegration" autocreate="true" generate="true">
            <deployment table="appintegration" typecode="20238"/>
            <attributes>
                <attribute qualifier="type" type="AppIntegrationType">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="name" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="standardIntegration" type="StandardAppIntegration">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="status" type="AppIntegrationStatus">
                    <modifiers read="true" write="true" search="false" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="documentation" type="PdfMedia">
                    <modifiers read="true" write="true" search="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AppLicense" extends="VariantProduct">
            <description>The actual version of an app, containing the actual manifestation (APK, compatibility) combined with the
                licenseType offered for this app version
            </description>
            <attributes>
                <attribute qualifier="name" type="localized:java.lang.String" redeclare="true">
                    <defaultvalue>null</defaultvalue>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="description" type="localized:java.lang.String" redeclare="true">
                    <defaultvalue>null</defaultvalue>
                    <modifiers read="true" write="false" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="licenseType" type="LicenseType">
                    <description>The license type for the AppLicense</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("LicenseType", "FULL")</defaultvalue>
                </attribute>
                <attribute qualifier="specifiedPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Price of a license</description>
                </attribute>
                <attribute qualifier="futurePrices" type="PriceRowCollectionType">
                    <persistence type="dynamic" attributeHandler="dynamicFuturePricesHandler"/>
                </attribute>
                <attribute qualifier="currentlyValidPrices" type="PriceRowCollectionType">
                    <persistence type="dynamic" attributeHandler="dynamicCurrentlyValidPricesHandler"/>
                </attribute>
                <attribute qualifier="icon" type="MediaContainer">
                    <persistence type="dynamic" attributeHandler="dynamicAppLicenseIconHandler"/>
                </attribute>
                <attribute qualifier="acquisitionCount" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Number of times that the particular Applicense has been acquired.</description>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="billingSystemStatus" type="BillingSystemStatus">
                    <description>Billing system synchronization status</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("BillingSystemStatus", "NEW")</defaultvalue>
                </attribute>
                <attribute qualifier="groupPriceUpdateStatus" type="GroupPriceUpdateStatus">
                    <description>Group Prices synchronization status</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("GroupPriceUpdateStatus", "NEW")</defaultvalue>
                </attribute>
                <attribute qualifier="purchasableInBillingSystem" type="java.lang.Boolean">
                    <persistence type="dynamic" attributeHandler="dynamicPurchasableInBillingSystemHandler"/>
                    <modifiers optional="false" read="true" write="false"/>
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                </attribute>
                <attribute qualifier="availabilityStatus" type="LicenseAvailabilityStatus">
                    <description>Status of a license availability defined by a user.</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("LicenseAvailabilityStatus", "UNPUBLISHED")</defaultvalue>
                </attribute>
            </attributes>
            <indexes>
                <index name="licenseType_idx">
                    <key attribute="baseProduct"/>
                    <key attribute="catalogVersion"/>
                    <key attribute="licenseType"/>
                    <key attribute="runtime"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="AppVersion">
            <description>A version of an app, consisting of the apk and the changelog.</description>
            <deployment table="appversion" typecode="20114"/>
            <custom-properties>
                <property name="catalogItemType">
                    <value>java.lang.Boolean.TRUE</value>
                </property>
                <property name="catalogVersionAttributeQualifier">
                    <value>"catalogVersion"</value>
                </property>
                <property name="uniqueKeyAttributeQualifier">
                    <value>"code"</value>
                </property>
                <property name="catalog.sync.default.root.type">
                    <value>Boolean.TRUE</value>
                </property>
                <property name="catalog.sync.default.root.type.order">
                    <value>Integer.valueOf(9)</value>
                </property>
            </custom-properties>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="catalogVersion" type="CatalogVersion">
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="apk" type="ApkMedia">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                </attribute>
                <attribute qualifier="changelog" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>nvarchar(max)</value>
                        </columntype>
                        <columntype database="hsqldb">
                            <value>LONGVARCHAR</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="exportRegulationAcknowledged" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="dualUse" type="boolean">
                    <description>Declares if this app version is Dual Use.</description>
                    <defaultvalue>false</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="eccn" type="java.lang.String">
                    <description>Export Control Classification Number for this app version.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Customer" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="emailAddress" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="IoTCustomer" extends="Customer">
            <description>Base type of IoT ecosystem customers</description>
        </itemtype>

        <itemtype code="Developer" extends="IoTCustomer">
            <description>Somebody who develops apps</description>
            <attributes>
                <attribute qualifier="integrator" type="Integrator">
                    <modifiers read="true" write="false" search="true"/>
                    <persistence type="dynamic" attributeHandler="dynamicIntegratorHandler"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Integrator" extends="IoTCustomer">
            <description>Somebody who buys apps</description>
            <attributes>
                <attribute qualifier="developer" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AbstractOrder" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="requestOrderTime" type="java.util.Date">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="eulaAcceptance" type="EulaAcceptance">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="activationMode" type="ActivationMode">
                    <description>License activation mode of the order.</description>
                    <persistence type="property"/>
                    <defaultvalue>em().getEnumerationValue("ActivationMode", "IMMEDIATE")</defaultvalue>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PaymentTransaction" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="type" type="PaymentTransactionType">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true"/>
                </attribute>
                <attribute qualifier="paymentInstrument" type="PaymentInstrument">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <description>Additional payment instrument information for this payment transaction</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="transRequestIdIdx">
                    <key attribute="requestId"/>
                </index>
            </indexes>
        </itemtype>


        <itemtype code="PaymentInstrument" generate="true" autocreate="true">
            <description>Additional payment instrument information for a payment transaction entry</description>
            <deployment table="paymentinstrument" typecode="20134"/>
            <attributes>
                <attribute qualifier="pspCustomerIdentifier" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Identifier for the customer with the used PSP</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SepaTransferPaymentInstrument" extends="PaymentInstrument">
            <attributes>
                <attribute qualifier="bankName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Bank Name</description>
                </attribute>
                <attribute qualifier="bic" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>BIC</description>
                </attribute>
                <attribute qualifier="iban" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <description>IBAN</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AchTransferPaymentInstrument" extends="PaymentInstrument">
            <attributes>
                <attribute qualifier="accountNumber" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <description>ACH Account Number</description>
                </attribute>
                <attribute qualifier="routingNumber" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <description>Routing Number</description>
                </attribute>
                <attribute qualifier="bankName" type="java.lang.String">
                    <description>Bank Name</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="bic" type="java.lang.String">
                    <description>BIC</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PaymentInfo" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="paymentProvider" type="PaymentProvider">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="companyScope" type="boolean">
                    <description>Denotes whether paymentInfo can be used by company colleagues </description>
                    <modifiers optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ZeroPaymentInfo" extends="PaymentInfo">
            <description>Payment information for free orders</description>
        </itemtype>

        <itemtype code="InvoiceBySellerPaymentInfo" extends="PaymentInfo">
            <description>Information about payments for orders taken by seller</description>
        </itemtype>

        <itemtype code="SepaCreditTransferPaymentInfo" extends="InvoicePaymentInfo">
            <description>Payment information for SEPA credit transfer payment</description>
            <attributes>
                <attribute qualifier="bankAccountIdentifier" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="bankIdentifier" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="bankName" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="sourceId" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Source ID is the reference information for the SepaCreditTransfer data stored in the external payment
                        provider.
                    </description>
                </attribute>
                <attribute qualifier="customerId" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Customer ID that is attached to a reusable source object
                    </description>
                </attribute>
                <attribute qualifier="accountHolder" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Account holder of the receiving SEPA bank account.</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="DirectDebitPaymentInfo" extends="PaymentInfo">
            <description>Payment Information for Direct Debit Payments</description>
        </itemtype>

        <itemtype code="SepaMandatePaymentInfo" extends="DirectDebitPaymentInfo">
            <description>Payment Information for Direct Debit Payments</description>
            <attributes>
                <attribute qualifier="mandateReference" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="IBAN" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="accountHolderName" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="dateOfSignature" type="java.util.Date">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AchInternationalCreditTransferPaymentInfo" extends="InvoicePaymentInfo">
            <description>Payment information for international ACH credit transfer payment</description>
        </itemtype>

        <itemtype code="StripeCreditCardPaymentInfo" extends="CreditCardPaymentInfo">
            <description>Payment information for Stripe credit card payment</description>
            <attributes>
                <attribute qualifier="ccOwner" type="java.lang.String" redeclare="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="number" type="java.lang.String" redeclare="true">
                    <!--  CAUTION: ONLY CHANGE THE VALUE OF ENCRYPTED WHEN YOU KNOW WHAT YOU DO !!! -->
                    <modifiers read="true" write="true" search="false" optional="true" encrypted="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validToMonth" type="java.lang.String" redeclare="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="validToYear" type="java.lang.String" redeclare="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="type" autocreate="true" type="CreditCardType" redeclare="true">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="stripeCcOwnerId" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="paymentMethodId" type="java.lang.String">
                    <description>Unique stripe source identifier</description>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="cardSummary" type="java.lang.String">
                    <description>Last 4 digits of the card number</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="subVariant" type="java.lang.String">
                    <description>Sub variant of the payment method</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="reusable" type="boolean">
                    <description>Denotes whether card is reusable in future checkouts</description>
                    <modifiers read="true" write="true" search="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="StripeConnectAccount" generate="true" autocreate="true">
            <description>A reference to an account at Stripe Connect for payouts</description>
            <deployment table="stripeconnectaccounts" typecode="20119"/>
            <attributes>
                <attribute qualifier="email" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers optional="true"/>
                </attribute>
                <attribute qualifier="accountId" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="user" type="Developer">
                    <persistence type="property"/>
                    <modifiers optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="status" type="StripeConnectAccountStatus">
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("StripeConnectAccountStatus", "IN_PROGRESS")</defaultvalue>
                </attribute>
                <attribute qualifier="accountManager" type="Employee">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="submissionDate" type="java.util.Date">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="provideInformationInvoked" type="boolean">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="exported" type="boolean">
                    <persistence type="property"/>
                    <description>true if the property has been exported to UMP/Pj-Eco</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="IoTCompany" extends="Company">
            <description>A company inside the IoT ecosystem</description>
            <attributes>
                <attribute redeclare="true" qualifier="country" type="Country">
                    <description>country</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="stripeCustomerId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>null</defaultvalue>
                    <description>ID of the customer object stored at Stripe</description>
                </attribute>
                <attribute qualifier="deactivationDate" type="java.util.Date">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="active" type="boolean">
                    <persistence type="dynamic" attributeHandler="companyDeactivationAttributeHandler"/>
                    <modifiers read="true" write="false" search="false" optional="true"/>
                </attribute>
                <attribute qualifier="approvalStatus" type="CompanyApprovalStatus">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("CompanyApprovalStatus", "UNAPPROVED")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="friendlyName" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="billingSystemStatus" type="BillingSystemStatus">
                    <description>Billing system synchronization status</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("BillingSystemStatus", "NEW")</defaultvalue>
                </attribute>
                <attribute qualifier="sepaEnabled" type="boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="manualAppApprovalEnabled" type="boolean">
                    <persistence type="property"/>
                    <defaultvalue>true</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="ownAppsPurchaseEnabled" type="boolean">
                    <description>when enabled,integrators from the company can buy their company apps</description>
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="store" type="BaseStore">
                    <description>the scope of the company.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="bpmdId" type="java.lang.String">
                    <description>The BPMD Id of the company is stored after synced with UMP.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="billingEmail" type="java.lang.String">
                    <description>The company email id used for billing.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="companyEmail" type="java.lang.String">
                    <description>The company email id used for company stuff.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="creditLimit" type="java.math.BigDecimal">
                    <description>The credit limit of the company set at UMP.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="isManaged" type="boolean">
                    <description>The isManaged company flag set at UMP.</description>
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                    <modifiers read="true" write="true" search="true"/>
                </attribute>
                <attribute qualifier="communicationLanguage" type="Language">
                    <description>Company default communication language (e.g. invoices or emails)</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="operationalStage" type="CompanyOperationalStage">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("CompanyOperationalStage", "AWAITING_DETAILS")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="stripeCustomerId_idx" unique="true">
                    <key attribute="stripeCustomerId"/>
                </index>
                <index name="storeIdx">
                    <key attribute="store"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="ApkSignature">
            <deployment table="ApkSignatures" typecode="20111"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>Business key for ApkSignature</description>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="certificateSha256" type="java.lang.String">
                    <description>SHA-256 of the used certificate</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="signatureVersion" type="SignatureVersion">
                    <description>APK signature version</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="validTo" type="java.util.Date">
                    <description>The data until the signature is valid</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="subjectCName" type="java.lang.String">
                    <description>Subject CN of the used certificate</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" initial="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ApkMedia" extends="CatalogUnawareMedia">
            <attributes>
                <attribute qualifier="packageName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="versionCode" type="java.lang.Long">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="versionName" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="signatures" type="SignatureList">
                    <description>APK signatures</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="permissions" type="PermissionList">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true" initial="false"/>
                </attribute>
                <attribute qualifier="sdkAddonVersion" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="minAndroidApiVersion" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="false"/>
                </attribute>
                <attribute qualifier="deviceCapabilities" type="DeviceCapabilitiesSet">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PdfMedia" extends="Media">
        </itemtype>

        <itemtype code="CatalogUnawarePdfMedia" extends="CatalogUnawareMedia">
        </itemtype>

        <itemtype code="UnusedPaymentRemovalCronJob" extends="CronJob">
            <attributes>
                <attribute type="java.lang.Integer" qualifier="age">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>Integer.valueOf(86400)</defaultvalue>
                    <description>After specified number of seconds payment info will be cleaned up. Default is 24 hour.</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="UnusedMediaRemovalCronJob" extends="CronJob">
            <attributes>
                <attribute type="java.lang.Integer" qualifier="age">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>Integer.valueOf(86400)</defaultvalue>
                    <description>After specified number of seconds unused media will be cleaned up. Default is 24 hour.</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PurchasedAppsAssignmentCronJob" extends="CronJob">
            <attributes>
                <attribute type="java.lang.Long" qualifier="days">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>Long.valueOf(14)</defaultvalue>
                    <description>All orders from now until now minus days will be checked to get the apps purchased by the companies.
                    </description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PaymentTransactionEntry" generate="false" autocreate="false">
            <attributes>
                <attribute qualifier="cartHash" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Cart" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="secondInvoiceNote" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="firstInvoiceNote" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Order" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="invoice" type="Media">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                    <model>
                        <getter name="invoice" deprecated="true" deprecatedSince="release_62" default="true"/>
                        <setter name="invoice" deprecated="true" deprecatedSince="release_62" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="externalOrderId" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="idempotencyKey" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="mcfOrder" type="boolean">
                    <description>The value true tells that order has item with MCF applicable.</description>
                    <modifiers read="true" write="true" optional="true" search="true"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="secondInvoiceNote" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="firstInvoiceNote" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true" search="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="ownFullAppOrder" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                    <description>value is set when cart is converted to order, 'true' for own app purchase.</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Permission">
            <description>The permissions on the device needed by the app</description>
            <deployment table="permissions" typecode="20105"/>
            <attributes>
                <attribute qualifier="technicalName" type="java.lang.String">
                    <description>The technical name of an app permission, e.g. android.permission.CAMERA</description>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <description>The display name of an app permission</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="icon" type="Media">
                    <description>The icon representing an app permission</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="blacklisted" type="boolean">
                    <description>Apks with blacklisted permissions cannot be uploaded</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="DeviceCapability">
            <description>The capabilities of the device required by the app</description>
            <deployment table="devicecapabilities" typecode="20132"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>The code of the capability as declared in the uses-feature element of the manifest, e.g.
                        com.securityandsafetythings.devicecapabilities.ptz
                    </description>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="displayName" type="localized:java.lang.String">
                    <description>The display name of the capability</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="codeIdx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="StoreContentDraft">
            <description>A draft of the meta data describing the store content of the app</description>
            <deployment table="storecontentdraft" typecode="20106"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <description>The localized name of the app</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="summary" type="localized:java.lang.String">
                    <description>Additional text attribute that holds localized brief description.</description>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="description" type="localized:java.lang.String">
                    <description>The localized description of the app</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>nvarchar(max)</value>
                        </columntype>
                        <columntype database="hsqldb">
                            <value>LONGVARCHAR</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="icon" type="MediaContainer">
                    <description>The app's icon to display in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="screenshots" type="MediaContainerList">
                    <description>The screenshots of the app</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="emailAddress" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Email address used for contact information of the app</description>
                </attribute>
                <attribute qualifier="supportPhoneNumber" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Support phone number used for contact information of the app</description>
                </attribute>
                <attribute qualifier="privacyPolicyUrl" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <description>Privacy policy URL used for contact information of the app</description>
                </attribute>
                <attribute qualifier="supportPageUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>URL for a support page or contact information of the app.</description>
                </attribute>
                <attribute qualifier="termsOfUseUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>URL for the Terms of Use of the app.</description>
                    <model>
                        <getter name="termsOfUseUrl" deprecated="true" deprecatedSince="release_74" default="true"/>
                        <setter name="termsOfUseUrl" deprecated="true" deprecatedSince="release_74" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="eula" type="Eula">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>End User License Agreement for the app.</description>
                </attribute>
                <attribute qualifier="productWebsiteUrl" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Website of the app or the company.</description>
                </attribute>
                <attribute qualifier="logo" type="Media">
                    <description>The former logo of the app. This field should not be used.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="video" type="AppVideo">
                    <description>The app's video to display in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CountriesAndPricesDraft">
            <description>A draft of the prices of an app for different currencies. At a later stage, it is planned to add availability by
                country to this item.
            </description>
            <deployment table="countriesandpricesdraft" typecode="20107"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabledInStore" type="boolean">
                    <defaultvalue>true</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <description>Flag to enable/disable app in store.</description>
                    <model>
                        <getter name="enabledInStore" deprecated="true" deprecatedSince="release_61" default="true"/>
                        <setter name="enabledInStore" deprecated="true" deprecatedSince="release_61" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="specifiedPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>
                        Price of the app in the currency associated with the country of the IoTCompany.
                        Deprecation Reason: Price is now part of a license
                    </description>
                    <model>
                        <getter name="specifiedPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                        <setter name="specifiedPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="subscriptionPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>
                        Price of the subscription app license in the currency associated with the country of the IoTCompany.
                        Deprecation Reason: Price is now part of a license
                    </description>
                    <model>
                        <getter name="subscriptionPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                        <setter name="subscriptionPrice" deprecated="true" deprecatedSince="release_67" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="pricingSaved" type="boolean">
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true"/>
                    <description>Whether the pricing has been saved; remove when pricing and availability data model is split</description>
                </attribute>
                <attribute qualifier="availabilitySaved" type="boolean">
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true"/>
                    <description>Whether the availability has been saved; remove when pricing and availability data model is split
                    </description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PriceDraft">
            <deployment table="pricedraft" typecode="20226"/>
            <custom-properties>
                <property name="catalogItemType"><value>java.lang.Boolean.FALSE</value></property>
            </custom-properties>
            <attributes>
                <attribute qualifier="currency" type="Currency">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" removable="false" initial="true"/>
                </attribute>
                <attribute qualifier="amount" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" removable="false" initial="true"/>
                </attribute>
                <attribute qualifier="validFrom" type="java.util.Date">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" removable="false" initial="true"/>
                </attribute>
                <attribute qualifier="minQuantity" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" removable="false" initial="true"/>
                    <defaultvalue>0</defaultvalue>
                </attribute>
                <attribute qualifier="scaledPriceDiscount" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" removable="false" initial="true"/>
                    <defaultvalue>0</defaultvalue>
                </attribute>
                <attribute qualifier="userPriceGroup" type="UserPriceGroup">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" initial="false" optional="true"/>
                </attribute>
                <attribute qualifier="billingPriceCode" type="java.lang.String">
                    <modifiers read="true" write="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="priceDraft_billingPriceCode_idx" unique="true">
                    <key attribute="billingPriceCode"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PriceRow" autocreate="false" generate="false">
            <description>Extending the PriceRow type from core with additional attributes.</description>
            <attributes>
                <attribute qualifier="scaledPriceDiscount" type="java.lang.Integer">
                    <description>Informational value containing the discount percentage for scaled price</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="billingRowId" type="java.lang.String">
                    <modifiers read="true" write="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="billingPriceCode" type="java.lang.String">
                    <modifiers read="true" write="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PendingProductInfo">
            <deployment table="pendingproductinfo" typecode="20227"/>
            <attributes>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="prices" type="PriceDraftSet">
                    <modifiers read="true" write="true" search="true" initial="false" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="groupPriceDraft" type="PriceDraftSet">
                    <modifiers read="true" write="true" search="true" initial="false" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="useLatestChargePlan" type="boolean">
                    <description>Indicates whether BRIM should use the latest charge plan starting from the next order.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AppVersionDraft">
            <description>A draft of an app version</description>
            <deployment table="appversiondraft" typecode="20108"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="apk" type="ApkMedia">
                    <description>The apk of the app version</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="changelog" type="localized:java.lang.String">
                    <description>The changelog describing the app version</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>nvarchar(max)</value>
                        </columntype>
                        <columntype database="hsqldb">
                            <value>LONGVARCHAR</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="approvalStatus" type="ArticleApprovalStatus">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("ArticleApprovalStatus", "DRAFT")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="exportRegulationAcknowledged" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="dualUse" type="boolean">
                    <description>Declares if this app version is Dual Use.</description>
                    <defaultvalue>false</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="eccn" type="java.lang.String">
                    <description>Export Control Classification Number for this app version.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="RejectionReason" generate="true" autocreate="true">
            <deployment table="rejectionreason" typecode="20122"/>
            <attributes>
                <attribute qualifier="rejectionCode" type="RejectionCode">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="comment" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AppDraft">
            <description>A draft of an app</description>
            <deployment table="appdraft" typecode="20109"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="storeContentDraft" type="StoreContentDraft">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="countriesAndPricesDraft" type="CountriesAndPricesDraft">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="approvalStatus" type="ArticleApprovalStatus">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("ArticleApprovalStatus", "DRAFT")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="storeAvailabilityMode" type="StoreAvailabilityMode">
                    <description>The app's availability in the store.</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("StoreAvailabilityMode", "UNAVAILABLE")</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="newCountryAddingAllowed" type="boolean">
                    <description>Developer's consent to make app available in new ecosystem country automatically</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ProductContainer">
            <description>A product container, which wraps an appDraft and an app</description>
            <deployment table="productcontainer" typecode="20110"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="appDraft" type="AppDraft">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="appVersionDraft" type="AppVersionDraft">
                    <modifiers read="true" write="true" search="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="app" type="App">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="title" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                    <model>
                        <getter name="title" deprecated="true" default="true"/>
                        <setter name="title" deprecated="true" default="true"/>
                    </model>
                </attribute>
                <attribute qualifier="createdBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="draft" type="boolean">
                    <persistence type="dynamic" attributeHandler="productContainerDraftStatusHandler"/>
                    <modifiers read="true" write="false"/>
                </attribute>
                <attribute qualifier="approved" type="boolean">
                    <persistence type="dynamic" attributeHandler="productContainerApprovedStatusHandler"/>
                    <modifiers read="true" write="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="title_company_idx" unique="true">
                    <key attribute="title"/>
                    <key attribute="company"/>
                </index>
                <index name="product_container_code_idx">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Country" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="currency" type="Currency">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabledForDevelopers" type="boolean">
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
                <attribute qualifier="enabledForIntegrators" type="boolean">
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
                <attribute qualifier="supportedPaymentProviders" type="PaymentProviderSet">
                    <description>The payment providers (STRIPE, ZERO, DPG) supported in the country.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="supportedPaymentMethods" type="PaymentMethodTypeSet">
                    <description>The payment methods (CREDIT_CARD, INVOICE, SEPA_CREDIT, ZERO, ACH_INTERNATIONAL, INVOICE_BY_SELLER)
                        supported in the country.
                    </description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="inEu" type="java.lang.Boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="blockedCountriesCommercial" type="CountryIsoCodeSet">
                    <description>
                        Companies from these countries cannot purchase commercial licenses sold by companies based in the country.
                    </description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>Collections.EMPTY_SET</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Media" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="used" type="java.lang.Boolean">
                    <description>Used to indicate whether Media Entities is eligible for cleaning (if not linked to an App)</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>true</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AppVideo" autocreate="true" generate="true">
            <deployment table="appvideo" typecode="20225"/>
            <attributes>
                <attribute qualifier="type" type="AppVideoType">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                    <description>The source service type: YOUTUBE, ...</description>
                </attribute>
                <attribute qualifier="status" type="AppVideoStatus">
                    <persistence type="property"/>
                    <description>The status of the video, found or not found</description>
                </attribute>
                <attribute qualifier="lastStatusCheck" type="java.util.Date">
                    <persistence type="property"/>
                    <description>The time when the status of the video was last checked</description>
                </attribute>
                <attribute qualifier="source" type="java.lang.String">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                    <description>The original url to the video, which can be used as a value for "src="</description>
                </attribute>
                <attribute qualifier="externalId" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                    <description>
                        The video provide may have the ID for the video, which could be used to target video specifically constructing the
                        URL
                        manually.
                    </description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="FeatureToggle" autocreate="true" generate="true">
            <deployment table="featuretoggle" typecode="20112"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabled" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CustomerReview" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="showCompany" type="boolean">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="showName" type="boolean">
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PriceLimit" autocreate="true" generate="true">
            <deployment table="pricelimit" typecode="20118"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="upperLimit" type="double">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>0.0</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="currency" type="Currency">
                    <description>The currency matching the code for non-fallback entries.</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SdkAddon">
            <deployment table="sdkaddon" typecode="20120"/>
            <attributes>
                <attribute type="java.lang.Integer" qualifier="version">
                    <modifiers read="true" write="false" initial="true" optional="false" search="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.Integer" qualifier="maxSupportedMinAndroidApiVersion">
                    <modifiers read="true" write="true" initial="true" optional="false" search="true"/>
                    <description>The maximum version of minSdkVersion (i.e. the AndroidApiVersion)
                        that may be defined in the AndroidManifest.xml for Apks that define this SdkAddon version.
                    </description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="minOsVersion">
                    <modifiers read="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="maxOsVersion">
                    <modifiers read="true" initial="false" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="unsupportedLegacyVersion" type="boolean">
                    <persistence type="property"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ReservedPackageName">
            <deployment table="reservedpackagename" typecode="20121"/>
            <attributes>
                <attribute qualifier="company" type="IoTCompany">
                    <modifiers read="true" write="true" optional="true" initial="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="packageNamePrefix" type="java.lang.String">
                    <modifiers read="true" write="true" optional="false" initial="true" unique="true"/>
                    <description>The prefix of the package name which is reserved</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AccountDeletion">
            <deployment table="accountdeletion" typecode="20123"/>
            <attributes>
                <attribute qualifier="uid" type="java.lang.String">
                    <modifiers read="true" write="true" optional="false" initial="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="processed" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="NavigationItem">
            <deployment table="navigationitem" typecode="20124"/>
            <attributes>
                <attribute qualifier="uid" type="java.lang.String">
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="itemCode" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="type" type="NavigationItemType">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="group" type="NavigationItemGroup">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="text" type="localized:java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="description" type="localized:java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="url" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="target" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="icon" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="index" type="java.lang.Integer">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabled" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="store" type="BaseStore">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="interpolatedUrl" type="boolean">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>

            <indexes>
                <index name="navigationItem_idx" unique="true">
                    <key attribute="type"/>
                    <key attribute="group"/>
                    <key attribute="index"/>
                    <key attribute="store"/>
                </index>
                <index name="navigationItem_itemCode_store_idx" unique="true">
                    <key attribute="itemCode"/>
                    <key attribute="store"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="AppLicenseDraft">
            <deployment table="applicensedraft" typecode="20125"/>
            <attributes>
                <attribute qualifier="licenseType" type="LicenseType">
                    <description>The license type for the AppLicense</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
                <attribute qualifier="availabilityStatus" type="LicenseAvailabilityStatus">
                    <description>Status of license availability defined by a user.</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("LicenseAvailabilityStatus", "UNPUBLISHED")</defaultvalue>
                </attribute>
                <attribute qualifier="submittedBy" type="Developer">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="specifiedPrice" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Price of a license</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="SupportedLicense" autocreate="true" generate="true">
            <deployment table="supportedlicense" typecode="20126"/>
            <attributes>
                <attribute qualifier="licenseType" type="LicenseType">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true" unique="true"/>
                </attribute>

                <attribute qualifier="enabled" type="boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="DeveloperPayout" autocreate="true" generate="true">
            <description>Actual payout, which was transferred to a developer company</description>
            <deployment table="developerpayout" typecode="20131"/>
            <attributes>
                <attribute qualifier="amount" type="java.lang.Long">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="currency" type="Currency">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="payoutDate" type="java.util.Date">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="true" initial="true"/>
                </attribute>
                <attribute qualifier="payoutId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="status" type="PayoutStatus">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("PayoutStatus", "CREATED")</defaultvalue>
                </attribute>
                <attribute qualifier="paymentProvider" type="PaymentProvider">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                    <defaultvalue>em().getEnumerationValue("PaymentProvider", "STRIPE")</defaultvalue>
                </attribute>
            </attributes>
            <indexes>
                <index name="payoutId_psp_idx" unique="true">
                    <key attribute="payoutId"/>
                    <key attribute="paymentProvider"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="SelfBillingInvoice" autocreate="true" generate="true">
            <description>Container for storing the self billing invoice of a developer and meta data (if available)</description>
            <deployment table="selfbillinginvoice" typecode="20128"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="externalId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers unique="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="displayName" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="document" type="Media">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="marketplaceShare" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Marketplace share as stated on the invoice document (the amount the Azena marketplace charges the seller)
                    </description>
                </attribute>
                <attribute qualifier="sellerShare" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Seller share as stated on the invoice document (this value is usually negative since the Azena marketplace
                        credits this amount to the seller)
                    </description>
                </attribute>
                <attribute qualifier="marketplaceTaxAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Tax amount charged by the marketplace to the seller (this value is usually negative since the Azena
                        marketplace credits this amount to the seller)
                    </description>
                </attribute>
                <attribute qualifier="status" type="SelfBillingInvoiceStatus">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>COMPLETED</defaultvalue>
                </attribute>
                <attribute qualifier="invoiceDate" type="java.util.Date">
                    <persistence type="property"/>
                    <description>Self Billing Invoice date received in the BRIM notification</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="externalId_idx" unique="true">
                    <key attribute="externalId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Invoice" autocreate="true" generate="true">
            <description>Container for storing the invoice</description>
            <deployment table="invoice" typecode="20133"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="externalId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers unique="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="displayName" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="document" type="Media">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="netAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Net total amount as stated on the invoice document</description>
                </attribute>
                <attribute qualifier="taxAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Total tax amount as stated on the invoice document</description>
                </attribute>
                <attribute qualifier="grossAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Gross total amount as stated on the invoice document</description>
                </attribute>
                <attribute qualifier="status" type="InvoiceStatus">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>INITIAL</defaultvalue>
                </attribute>
                <attribute qualifier="invoiceDate" type="java.util.Date">
                    <persistence type="property"/>
                    <description>Invoice date received in the BRIM notification</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="externalId_idx" unique="true">
                    <key attribute="externalId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="InvoiceItem" autocreate="true" generate="true">
            <description>Container for storing the invoice items</description>
            <deployment table="invoiceItem" typecode="20135"></deployment>
            <attributes>
                <attribute qualifier="positionId" type="java.lang.Integer">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="productId" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="quantity" type="java.lang.Long">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="netPrice" type="java.math.BigDecimal">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="currency" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="UserSpecificOfflineToken" generate="true" autocreate="true">
            <deployment table="UserSpecificOfflineToken" typecode="20218"/>
            <attributes>
                <attribute qualifier="userId" type="java.lang.String">
                    <description>UMP UserId</description>
                    <modifiers unique="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="offlineToken" type="java.lang.Object">
                    <description>token value</description>
                    <modifiers read="true" write="true" search="false" initial="true"/>
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>BLOB</value>
                        </columntype>
                        <columntype>
                            <value>java.io.Serializable</value>
                        </columntype>
                    </persistence>
                </attribute>
            </attributes>
            <indexes>
                <index name="userIdIdx" unique="true">
                    <key attribute="userId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Industry" autocreate="true" generate="true">
            <deployment table="industry" typecode="20219"/>
            <attributes>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <description>The localized name of the industry</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="index" type="java.lang.Integer">
                    <description>Order index of industry item</description>
                    <defaultvalue>0</defaultvalue>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="enabled" type="boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>true</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="UseCase" generate="true" autocreate="true"
                  jaloclass="com.sast.cis.core.jalo.UseCase">
            <deployment table="usecase" typecode="20222"/>
            <attributes>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <modifiers read="true" write="true" search="true" initial="true"/>
                    <description>name of the use case</description>
                    <persistence type="property">
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="index" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <defaultvalue>0</defaultvalue>
                </attribute>
                <attribute qualifier="enabled" type="boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>true</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="VolumeDiscount" generate="true" autocreate="true"
                  jaloclass="com.sast.cis.core.jalo.VolumeDiscount">
            <deployment table="volumeDiscount" typecode="20229"/>
            <attributes>
                <attribute qualifier="minQuantity" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <description>Minimum quantity that need to be ordered for the applied discounted price</description>
                </attribute>
                <attribute qualifier="discount" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <description>Discount on the base price for the scale set at min quantity field.</description>
                </attribute>
                <attribute qualifier="active" type="boolean">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <defaultvalue>false</defaultvalue>
                </attribute>
            </attributes>
            <indexes>
                <index name="volumediscount_idx" unique="true">
                    <key attribute="appLicense"/>
                    <key attribute="minQuantity"/>
                    <key attribute="discount"/>
                    <key attribute="active"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="VolumeDiscountDraft" generate="true" autocreate="true"
                  jaloclass="com.sast.cis.core.jalo.VolumeDiscountDraft">
            <deployment table="volumediscountdraft" typecode="20230"/>
            <attributes>
                <attribute qualifier="minQuantity" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <description>Minimum quantity that need to be ordered for the applied discounted price</description>
                </attribute>
                <attribute qualifier="discount" type="java.lang.Integer">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" unique="true"/>
                    <description>Discount on the base price for the scale set at min quantity field.</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="volumediscountdraft_idx" unique="true">
                    <key attribute="appLicenseDraft"/>
                    <key attribute="minQuantity"/>
                    <key attribute="discount"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="Subscription" jaloclass="com.sast.cis.core.jalo.Subscription" generate="true" autocreate="true">
            <deployment table="Subscriptions" typecode="20231"/>
            <attributes>
                <attribute type="java.lang.String" qualifier="uuid">
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="productCode">
                    <description>Product Code</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="orderNumber">
                    <description>Order Identifier</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.Integer" qualifier="entryNumber">
                    <description>Order Entry Identifier</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.util.Date" qualifier="startDate">
                    <description>Start date</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.util.Date" qualifier="endDate">
                    <description>End date</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.util.Date" qualifier="cancelledDate">
                    <description>Cancellation Date</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="timezone">
                    <description>
                        Time zone for the contract start and end dates. The time zone is provided by BRIM in the Order creation response.
                    </description>
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="contractTerminationRule" type="ContractTerminationRule">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Termination rule for this subscription (defines length of contract periods etc...)</description>
                </attribute>
                <attribute qualifier="billingPriceCode" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Current billing price code used for this subscription contract</description>
                </attribute>
            </attributes>

            <indexes>
                <index name="subscription_idx" unique="true">
                    <key attribute="uuid"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PriceRecalculationCronjob" generate="true" autocreate="true" extends="CronJob">
            <attributes>
                <attribute qualifier="ignoreLicenses" type="LicenseTypeSet">
                    <description>price recalcualtion is not applied to configured set of LicenseTypes</description>
                    <persistence type="property"/>
                    <defaultvalue>Collections.EMPTY_SET</defaultvalue>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="MinimumCommission" jaloclass="com.sast.cis.core.jalo.MinimumCommission" autocreate="true" generate="true">
            <deployment table="minimumcommission" typecode="20234"/>
            <attributes>
                <attribute qualifier="fee" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                    <description>The minimum commission fee that will be charged.</description>
                </attribute>
                <attribute qualifier="licenseType" type="LicenseType">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                    <description>The license type for the commission fee.</description>
                </attribute>
                <attribute qualifier="currency" type="Currency">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" optional="false"/>
                    <description>The currency of the commission fee.</description>
                </attribute>
                <attribute qualifier="sastCommissionPercentage" type="java.lang.Double">
                    <persistence type="property"/>
                    <modifiers read="true" write="false" optional="true" initial="true"/>
                    <defaultvalue>Double.valueOf(30)</defaultvalue>
                    <description>The SAST commission (in percentage).</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="minimumcommission_idx" unique="true">
                    <key attribute="licenseType"/>
                    <key attribute="currency"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PspSellerAccount" abstract="true">
            <deployment table="pspselleraccount" typecode="20236"/>
            <attributes>
                <attribute qualifier="accountId" type="java.lang.String">
                    <description>The account Id of PSP seller account</description>
                    <modifiers read="true" write="true" search="true" optional="true" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="user" type="Developer">
                    <description>The user of seller company who initiated the onboarding process.</description>
                    <persistence type="property"/>
                    <modifiers optional="true" initial="true" write="true"/>
                </attribute>
                <attribute qualifier="status" type="PspSellerAccountStatus">
                    <description>The status of th PSP seller account (ACTIVE, REJECTED, DISABLED, ONBOARDING).</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="billingSystemStatus" type="BillingSystemStatus">
                    <description>Billing system synchronization status</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                    <defaultvalue>em().getEnumerationValue("BillingSystemStatus", "NEW")</defaultvalue>
                </attribute>
                <attribute qualifier="paymentProvider" type="PaymentProvider">
                    <description>The payment provider (STRIPE, ZERO, DPG, BOSCH_TRANSFER, PGW, ADYEN) of the account.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="pspselleraccount_idx" unique="true">
                    <key attribute="accountId"/>
                    <key attribute="paymentProvider"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PrivateOfferRequest"
                  autocreate="true"
                  generate="true">
            <deployment table="privateofferrequest" typecode="20240"/>
            <attributes>
                <attribute qualifier="message" type="java.lang.String" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="false"/>
                </attribute>
                <attribute qualifier="messageText" type="java.lang.String" autocreate="true" generate="true">
                    <persistence type="property">
                        <columntype database="oracle">
                            <value>CLOB</value>
                        </columntype>
                        <columntype database="sap">
                            <value>NCLOB</value>
                        </columntype>
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype database="sqlserver">
                            <value>nvarchar(max)</value>
                        </columntype>
                        <columntype database="hsqldb">
                            <value>LONGVARCHAR</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                    <modifiers read="true" write="true" search="false" optional="true"/>
                </attribute>
                <attribute qualifier="projectRegistration" type="PrivateOfferProjectRegistration">
                    <modifiers read="true" write="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PrivateOfferRequestItem"
                  autocreate="true"
                  generate="true">
            <deployment table="privateofferrequestitem" typecode="20241"/>
            <attributes>
                <attribute qualifier="requestPrice" type="java.math.BigDecimal" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="false"/>
                </attribute>
                <attribute qualifier="originalPrice" type="java.math.BigDecimal" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="false"/>
                </attribute>
                <attribute qualifier="quantity" type="java.lang.Integer" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="false"/>
                </attribute>
                <attribute qualifier="licenseType" type="LicenseType" autocreate="true" generate="true">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="false" optional="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CountrySyncCronJob" extends="CronJob"/>
        <itemtype code="CompanySyncCronJob" extends="CronJob"/>

        <itemtype code="ToolCompany">
            <deployment table="toolcompany" typecode="20242"/>
            <attributes>
                <attribute qualifier="company" type="IoTCompany">
                    <description>IoTCompany used to create Tool licenses.</description>
                    <modifiers read="true" write="true" optional="true" initial="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="toolcompany_company_unq_idx" unique="true">
                    <key attribute="company"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="LicenseActivation" generate="true" autocreate="true">
            <description>The App License activation status for Integrator companies.
                App licenses of certain types (TOOL) are not acquired via orders, but are activated by the Integrator company.
                When An App license is activated, the Integrator company can use the App on an unlimited number of devices.
            </description>
            <deployment table="licenseactivation" typecode="20244"/>
            <attributes>
                <attribute qualifier="company" type="IoTCompany">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="appLicense" type="AppLicense">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="activationStatus" type="LicenseActivationStatus">
                    <description>License activation status for Integrator company</description>
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="licenseactivation_company_appLicense_unq_idx" unique="true">
                    <key attribute="company"/>
                    <key attribute="appLicense"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="OldCartRemovalCronJob" autocreate="true" generate="true" extends="CronJob">
            <attributes>
                <attribute type="BaseSiteCollection" qualifier="sites">
                    <modifiers/>
                    <persistence type="property"/>
                    <description>BaseSites for which old carts should be removed</description>
                </attribute>
                <attribute type="java.lang.Integer" qualifier="cartRemovalAge">
                    <modifiers/>
                    <persistence type="property"/>
                    <defaultvalue>Integer.valueOf(2419200)</defaultvalue>
                    <description>After specified number of seconds carts will be cleaned up. Default is 28 days.</description>
                </attribute>
                <attribute type="java.lang.Integer" qualifier="anonymousCartRemovalAge">
                    <modifiers/>
                    <persistence type="property"/>
                    <defaultvalue>Integer.valueOf(1209600)</defaultvalue>
                    <description>After specified number of seconds carts will be cleaned up. Default is 14 days.</description>
                </attribute>
            </attributes>
        </itemtype>
        <itemtype code="FollowAppSubscription" generate="true" autocreate="true">
            <description>The Follow App Subscription for Integrators.
                when subscribed, Integrators are notified when the App version or license terms changes.
            </description>
            <deployment table="followappsubscription" typecode="20248"/>
            <attributes>
                <attribute qualifier="integrator" type="Integrator">
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="appCode" type="java.lang.String">
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="followAppSubscriptionStatus" type="FollowAppSubscriptionStatus">
                    <description>Follow App Subscription status of an Integrator for the given app</description>
                    <modifiers read="true" write="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="followappsubscription_integrator_appcode_unq_idx" unique="true">
                    <key attribute="integrator"/>
                    <key attribute="appCode"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PrivateOfferProjectRegistration" generate="true" autocreate="true">
            <description>Private offer project Information</description>
            <deployment table="privateofferprojectreg" typecode="20250"/>
            <attributes>
                <attribute qualifier="projectName" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="customerName" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="plannedStartDate" type="java.util.Date">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="siteAddress" type="PrivateOfferProjectAddress">
                    <modifiers read="true" write="true" optional="true" partof="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PrivateOfferProjectAddress" generate="true" autocreate="true">
            <description>Private offer project address Information</description>
            <deployment table="privateofferprojectaddr" typecode="20252"/>
            <attributes>
                <attribute qualifier="country" type="Country">
                    <description>country</description>
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="city" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="postalCode" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="line1" type="java.lang.String">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>


        <itemtype code="InvoiceCreditNote" autocreate="true" generate="true">
            <description>Container for storing an invoice's credit note</description>
            <deployment table="invoicecreditnote" typecode="20254"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="externalId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers unique="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="displayName" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="document" type="Media">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="netAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="taxAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="grossAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="type" type="CreditNoteType">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>
                <attribute qualifier="issuanceDate" type="java.util.Date">
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="externalId_idx" unique="true">
                    <key attribute="externalId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="SelfBillingInvoiceCreditNote" autocreate="true" generate="true">
            <description>Container for storing a self billing invoice's credit note</description>
            <deployment table="sbicreditnote" typecode="20256"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="externalId" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers unique="true" optional="false" initial="true"/>
                </attribute>
                <attribute qualifier="displayName" type="java.lang.String">
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="document" type="Media">
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="marketplaceShare" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Marketplace share as stated on the credit note document
                        (the amount the Azena marketplace charges the seller)
                    </description>
                </attribute>
                <attribute qualifier="sellerShare" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Seller share as stated on the credit note document
                        (this value is usually negative since the Azena marketplace credits this amount to the seller)
                    </description>
                </attribute>
                <attribute qualifier="marketplaceTaxAmount" type="java.math.BigDecimal">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Tax amount charged by the marketplace to the seller (this value is usually negative since the Azena
                        marketplace credits this amount to the seller)
                    </description>
                </attribute>

                <attribute qualifier="type" type="CreditNoteType">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                </attribute>

                <attribute qualifier="issuanceDate" type="java.util.Date">
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="externalId_idx" unique="true">
                    <key attribute="externalId"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="CronjobConfiguration" autocreate="true" generate="true" extends="GenericItem">
            <deployment table="cronjobconfig" typecode="20258"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>Configuration code</description>
                    <modifiers optional="false" unique="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="baseStore" type="BaseStore" autocreate="true">
                    <description>Store is needed for setting right userPriceGroup, taxes and for visibility of products.</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
                <attribute qualifier="productCatalogVersion" type="CatalogVersion" autocreate="true">
                    <description>For exporting the right products we need to select the right catalog version.</description>
                    <persistence type="property"/>
                    <modifiers optional="false"/>
                </attribute>
            </attributes>
        </itemtype>
        <itemtype code="BaseStore" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="defaultTerminationRule" type="ContractTerminationRule">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ContractTerminationRule" autocreate="true" generate="true" extends="GenericItem">
            <deployment table="contracttermrule" typecode="20259"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers optional="false" unique="true" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="noticePeriod" type="TerminationRulePeriod">
                    <modifiers optional="true" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="initialPeriod" type="TerminationRulePeriod">
                    <modifiers optional="true" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="followUpPeriod" type="TerminationRulePeriod">
                    <modifiers optional="true" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="gracePeriod" type="TerminationRulePeriod">
                    <modifiers optional="true" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="fixedPeriod" type="TerminationRulePeriod">
                    <modifiers optional="true" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="code_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="TerminationRulePeriod" autocreate="true" generate="true">
            <deployment table="termruleperiods" typecode="20260"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <modifiers optional="false" unique="true" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="value" type="int">
                    <modifiers optional="false" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="unit" type="TerminationRuleUnit">
                    <modifiers optional="false" unique="false" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>
        <itemtype code="CronJob" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="configuration" type="CronjobConfiguration">
                    <description>Export configuration</description>
                    <modifiers optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>
        <itemtype code="BlockedApkSignerCertSubjectCName">
            <description>Blocked APK signer certificates subject CNs</description>
            <deployment table="blockedapksigcertsubjcn" typecode="20262"/>
            <attributes>
                <attribute qualifier="cnValue" type="java.lang.String">
                    <description>Represents the Common Name (CN) value</description>
                    <modifiers optional="false" unique="true" initial="true" read="true" write="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="cn_idx" unique="true">
                    <key attribute="cnValue"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="ClassificationAttributeValue" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="termBoost" type="java.lang.Float">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="icon" type="CatalogUnawareMediaContainer">
                    <description>The attribute value's icon.</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="PostSubscriptionPriceUpdateEvent" generate="true" autocreate="true">
            <description>Post price update Event</description>
            <deployment table="postsubspriceupdateevent" typecode="20264"/>
            <attributes>
                <attribute qualifier="eventId" type="java.lang.String">
                    <modifiers unique="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="appLicense" type="AppLicense">
                    <description>App license subject to price change</description>
                    <modifiers read="true" write="true" search="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="eventStatus" type="EventProcessingStatus">
                    <modifiers read="true" write="true" initial="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="eventId_idx" unique="true">
                    <key attribute="eventId"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="CountryStoreConfiguration" generate="true" autocreate="true">
            <description>Country Store Configuration</description>
            <deployment table="CountryStoreConf" typecode="20265"/>
            <attributes>
                <attribute qualifier="baseStore" generate="true" autocreate="true"
                           type="BaseStore">
                    <description>BaseStore of the configuration</description>
                    <persistence type="property"/>
                    <modifiers optional="false" unique="true"/>
                </attribute>
                <attribute qualifier="country" type="Country">
                    <description>Country of the configuration</description>
                    <modifiers unique="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="languages" type="LanguageSet">
                    <description>supported languages of the country for the base store</description>
                    <modifiers read="true" write="true" initial="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="defaultLanguage" type="Language">
                    <description>default language of the country in a base store</description>
                    <modifiers read="true" write="true" initial="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="storefrontEnabled" type="java.lang.Boolean">
                    <description>define if a country is enabled for the tenant</description>
                    <modifiers read="true" write="true" initial="true" optional="true"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="importedCompaniesCanBuy" type="java.lang.Boolean">
                    <description>define if imported companies can make purchases</description>
                    <modifiers read="true" write="true" initial="true" optional="true"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="country_store_unique_idx" unique="true">
                    <key attribute="baseStore"/>
                    <key attribute="country"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="BuyerContract" abstract="true">
            <deployment table="BuyerContract" typecode="20266"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>Contract Identifier</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
                <attribute qualifier="contractTerminationRule" type="ContractTerminationRule">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Termination rule for this contract</description>
                </attribute>
                <attribute type="java.util.Date" qualifier="startDate">
                    <description>Start date</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.util.Date" qualifier="endDate">
                    <description>End date</description>
                    <persistence type="property"/>
                </attribute>
                <attribute type="java.lang.String" qualifier="timezone">
                    <description>
                        Time zone for the contract start and end dates. The time zone is provided by BRIM in the Order creation response.
                    </description>
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="billingPriceCode" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Current billing price code used for this subscription contract</description>
                </attribute>
            </attributes>
            <indexes>
                <index name="buyerContractCode_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="FixedTermContract" generate="true" autocreate="true" extends="BuyerContract"/>

        <itemtype code="SubscriptionContract" generate="true" autocreate="true" extends="BuyerContract">
            <attributes>
                <attribute qualifier="legacySubscriptionId" type="java.lang.String">
                    <description>UUID information from legacy subscription model</description>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Runtime" generate="true" autocreate="true">
            <description>Product variant runtime. For License products represents the validity duration.</description>
            <deployment table="runtime" typecode="20267"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>Runtime Identifier</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
                <attribute qualifier="name" type="localized:java.lang.String">
                    <description>The localized name of the Runtime</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="description" type="java.lang.String">
                    <description>The description of the Runtime</description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property">
                        <columntype database="mysql">
                            <value>MEDIUMTEXT</value>
                        </columntype>
                        <columntype>
                            <value>HYBRIS.LONG_STRING</value>
                        </columntype>
                    </persistence>
                </attribute>
                <attribute qualifier="defaultRuntimeTerminationRule" type="ContractTerminationRule">
                    <description>Default contract termination rule for the Runtime</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="runtime_code_unique_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="BundleInfo">
            <description>Holds the information about the bundle, number of license in the bundle.</description>
            <deployment table="bundleinfos" typecode="26107"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>Bundle Identifier</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>
                <attribute qualifier="name" type="java.lang.String">
                    <description>Name of the Bundle ( eg. S for small Bundle, M for Medium Sized Bundle, XXL)</description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <persistence type="property"/>
                    <defaultvalue>S</defaultvalue>
                </attribute>
                <attribute qualifier="size" type="int">
                    <description>The size (number of license) of the Bundle</description>
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                    <defaultvalue>1</defaultvalue>
                </attribute>
            </attributes>
            <indexes>
                <index name="bundle_info_code_unique_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="VariantProduct" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="runtime" type="Runtime">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
                <attribute qualifier="bundleInfo" type="BundleInfo">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="Category" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="iconCode" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Category icon code</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="AbstractOrderEntry" autocreate="false" generate="false">
            <attributes>
                <attribute qualifier="billingPriceCode" type="java.lang.String">
                    <persistence type="property"/>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <description>Billing price code of the price determined for order calculation</description>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="CatalogUnawareMediaContainer" autocreate="true" generate="true" extends="MediaContainer">
            <custom-properties>
                <property name="catalogItemType"><value>java.lang.Boolean.FALSE</value></property>
            </custom-properties>
            <attributes>
                <attribute qualifier="catalogVersion" type="CatalogVersion" redeclare="true">
                    <persistence type="property"/>
                    <modifiers optional="true" initial="false"/>
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="EulaAcceptance" autocreate="true" generate="true">
            <deployment table="eulaacceptance" typecode="20272"/>
            <attributes>
                <attribute qualifier="user" type="User">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="acceptanceTimestamp" type="java.util.Date">
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="acceptedEulasUrls" type="EulaUrlSet">
                    <description>URLs of the accepted EULAs</description>
                    <modifiers read="true" write="true" search="true" optional="false" initial="true"/>
                    <defaultvalue>Collections.EMPTY_SET</defaultvalue>
                    <persistence type="property"/>
                </attribute>

            </attributes>
        </itemtype>

        <itemtype code="NavigationItemAttribute">
            <description>Navigation item attribute. Will be added as HTML attribute to the navigation item link.</description>
            <deployment table="navigationitemattribute" typecode="20274"/>
            <attributes>
                <attribute qualifier="name" type="java.lang.String">
                    <description>Attribute name (mandatory)</description>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="value" type="java.lang.String">
                    <description>Attribute value (optional). A null or blank value must be interpreted as boolean HTML attribute with value true.</description>
                    <modifiers read="true" write="true" search="true" optional="true" initial="false"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="navigation_item_attribute_idx" unique="true">
                    <key attribute="name"/>
                    <key attribute="navigationItem"/>
                </index>
            </indexes>
        </itemtype>

        <itemtype code="PaymentInfoDraft">
            <description>
                PaymentInfoDraft is used for the creation of standalone Payment Infos.
                The Draft is later transformed into a PaymentInfo.
            </description>
            <deployment table="paymentinfodraft" typecode="20276"/>
            <attributes>
                <attribute qualifier="code" type="java.lang.String">
                    <description>PaymentInfoDraft Identifier</description>
                    <persistence type="property"/>
                    <modifiers read="true" write="false" search="true" optional="false" initial="true" unique="true"/>
                </attribute>

                <attribute qualifier="integrator" type="Integrator">
                    <description>PaymentInfo owner</description>
                    <modifiers read="true" write="true" search="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="creationStatus" type="PaymentInfoDraftCreationStatus">
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="paymentProvider" type="PaymentProvider">
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="paymentMethodType" type="PaymentMethodType">
                    <modifiers read="true" write="true" optional="false" initial="true"/>
                    <persistence type="property"/>
                </attribute>

                <attribute qualifier="resultingPaymentInfo" type="PaymentInfo">
                    <modifiers read="true" write="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>

            </attributes>
            <indexes>
                <index name="payment_info_draft_code_idx" unique="true">
                    <key attribute="code"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="CountryMigrationConfiguration" generate="true" autocreate="true">
            <description>Country Migration Configuration</description>
            <deployment table="countrymigrationconf" typecode="20278"/>
            <attributes>
                <attribute qualifier="country" type="Country">
                    <description>Country of the configuration</description>
                    <modifiers unique="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="contractStartDate" type="java.util.Date">
                    <description>defines the start date for contracts of a country migration</description>
                    <modifiers read="true" write="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="sepaMandateCreationDueDate" type="java.util.Date">
                    <description>
                        Due date for the creation of a SEPA DD Payment Info to be used for the migration
                    </description>
                    <modifiers read="true" write="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="sepaDDRecommended" type="boolean">
                    <description>
                        Determines whether customers should be prompted to set up SEPA DD as a payment method to be used for the migration
                    </description>
                    <modifiers read="true" write="true" search="true" optional="false"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="firstMigrationNoticeDate" type="java.util.Date">
                    <description>
                        Represents the date when customers were first informed about an impending contract migration
                    </description>
                    <modifiers read="true" write="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="purchaseAllowedFromDate" type="java.util.Date">
                    <description>
                        Represents the date when migrated customers are allowed to buy in the store
                    </description>
                    <modifiers read="true" write="true" initial="true" optional="false"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="lmpEndDateForFutureDatedContracts" type="java.util.Date">
                    <description>
                        End date for cancelled non-active UPM contracts.
                    </description>
                    <modifiers read="true" write="true" initial="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="allowPurchasesWithoutMigratedContracts" type="java.lang.Boolean">
                    <description>define if purchases are allowed for companies without migrated contracts</description>
                    <modifiers read="true" write="true" initial="true" optional="true"/>
                    <defaultvalue>false</defaultvalue>
                    <persistence type="property"/>
                </attribute>
            </attributes>
            <indexes>
                <index name="country_migration_conf_country_unique_idx" unique="true">
                    <key attribute="country"/>
                </index>
            </indexes>
        </itemtype>
        <itemtype code="OpenOrderCancelBusinessProcess" autocreate="true" generate="true" extends="BusinessProcess">
            <description>
                Process for cancelling an Order with OPEN status.
            </description>
        </itemtype>
        <itemtype code="TokenizedSepaDirectDebitPaymentInfo" extends="SepaMandatePaymentInfo">
            <description>Payment information for Tokenized Direct Debit payments</description>
            <attributes>
                <attribute qualifier="shopperReference" type="java.lang.String">
                    <description>
                        Shopper Reference.
                        Can be either a companyUid or a userUid, depends on the scope of paymentInfo.
                    </description>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="recurringReference" type="java.lang.String">
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
                <attribute qualifier="pspTokenizationReference" type="java.lang.String">
                    <description>
                        PSP tokenization transaction reference.
                    </description>
                    <modifiers read="true" write="true" search="true" optional="true"/>
                    <persistence type="property"/>
                </attribute>
            </attributes>
        </itemtype>
    </itemtypes>
</items>
