<?xml version="1.0" encoding="ISO-8859-1"?>

<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="beans.xsd">
    <bean class="de.hybris.platform.commercefacades.product.data.ChangelogData">
        <property name="version" type="String"/>
        <property name="description" type="String"/>
        <property name="creationDate" type="java.util.Date"/>
    </bean>

    <bean class="com.sast.cis.core.data.IotCompanyData">
        <property name="name" type="String"/>
        <property name="friendlyName" type="String"/>
        <property name="companyUid" type="String"/>
        <property name="companyCountry" type="String"/>
        <property name="hasPublishedProfile" type="boolean"/>
        <property name="profileUrl" type="String"/>
        <property name="managedAccount" type="boolean"/>
        <property name="companyApproved" type="boolean"/>
        <property name="userGroup" type="String"/>
        <property name="distributor" type="com.sast.cis.core.data.UmpDistributorData"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppContactData">
        <property name="emailAddress" type="String"/>
        <property name="supportPageUrl" type="String"/>
        <property name="supportPhoneNumber" type="String"/>
        <property name="privacyPolicyUrl" type="String"/>
        <property name="productWebsiteUrl" type="String"/>
        <property name="eula" type="com.sast.cis.core.data.EulaData"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.ProductData" superEquals="true">
        <property name="company" type="com.sast.cis.core.data.IotCompanyData"/>
        <property name="appContactInfo" type="com.sast.cis.core.data.AppContactData"/>
        <property name="versionName" type="String"/>
        <property name="versionCode" type="Long" equals="true"/>
        <property name="appApk" type="String"/>
        <property name="changelogs" type="java.util.List&lt;ChangelogData&gt;"/>
        <property name="approvalStatusTheme" type="String"/>
        <property name="approvalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="packageName" type="String"/>
        <property name="permissions" type="List&lt;com.sast.cis.core.data.PermissionData&gt;"/>
        <property name="deviceCapabilities" type="Set&lt;com.sast.cis.core.data.DeviceCapabilityData&gt;"/>
        <property name="acquisitionCount" type="int"/>
        <property name="licenses" type="List&lt;com.sast.cis.core.data.ProductLicenseData&gt;"/>
        <property name="pdfDocuments" type="List&lt;com.sast.cis.core.data.PdfData&gt;"/>
        <property name="sdkAddonVersion" type="Integer"/>
        <property name="dualUse" type="boolean"/>
        <property name="enabledCountries" type="java.util.Set&lt;de.hybris.platform.commercefacades.user.data.CountryData&gt;"/>
        <property name="video" type="com.sast.cis.core.data.AppVideoData"/>
        <property name="storeAvailabilityMode" type="com.sast.cis.core.enums.StoreAvailabilityMode"/>
        <property name="supportedLicenseTypes" type="java.util.List&lt;com.sast.cis.core.enums.LicenseType&gt;"/>
        <property name="appIntegrations" type="List&lt;com.sast.cis.core.data.PublicAppIntegrationData&gt;"/>
        <property name="followAppData" type="com.sast.cis.core.data.FollowAppData"/>
        <property name="ownCompanyAppAndCanBuy" type="boolean"/>
        <property name="integratorCountryBlockedInDeveloperCountry" type="boolean"/>
        <property name="licenseRuntimes" type="java.util.List&lt;String&gt;"/>
        <property name="countryEulas" type="java.util.List&lt;com.sast.cis.core.data.CountryEulaData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.SimpleProductData">
        <property name="code" type="String"/>
        <property name="url" type="String"/>
        <property name="name" type="String"/>
        <property name="shortDescription" type="String"/>
        <property name="logoUrl" type="String"/>
        <property name="acquisitionCount" type="int"/>
        <property name="enabledCountries" type="java.util.Set&lt;de.hybris.platform.commercefacades.user.data.CountryData&gt;"/>
        <property name="company" type="com.sast.cis.core.data.IotCompanyData"/>
        <property name="storeAvailabilityMode" type="com.sast.cis.core.enums.StoreAvailabilityMode"/>
        <property name="licenses" type="java.util.List&lt;ProductLicenseData&gt;"/>
        <property name="licenseRuntimes" type="java.util.List&lt;String&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.DetailProductData" extends="SimpleProductData">
        <property name="appApk" type="String"/>
        <property name="approvalStatusTheme" type="String"/>
        <property name="description" type="String"/>
        <property name="dualUse" type="boolean"/>
        <property name="dualUseInfoText" type="String"/>
        <property name="packageName" type="String"/>
        <property name="postReviewUrl" type="String"/>
        <property name="postUrl" type="String"/>
        <property name="sdkAddonVersion" type="Integer"/>
        <property name="versionName" type="String"/>
        <property name="versionCode" type="Long"/>
        <property name="appContactInfo" type="AppContactData"/>
        <property name="reviewStatus" type="ReviewStatus"/>
        <property name="osCompatibility" type="OsCompatibilityData"/>
        <property name="permissions" type="java.util.List&lt;PermissionData&gt;"/>
        <property name="deviceCapabilities" type="java.util.Set&lt;DeviceCapabilityData&gt;"/>
        <property name="pdfDocuments" type="java.util.List&lt;PdfData&gt;"/>
        <property name="galleryItems" type="java.util.List&lt;GalleryItemData&gt;"/>
        <property name="approvalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="reviews" type="java.util.Collection&lt;de.hybris.platform.commercefacades.product.data.ReviewData&gt;"/>
        <property name="changelogs" type="java.util.List&lt;de.hybris.platform.commercefacades.product.data.ChangelogData&gt;"/>
        <property name="video" type="AppVideoData"/>
        <property name="supportedLicenses" type="List&lt;com.sast.cis.core.data.ProductLicenseData&gt;"/>
        <property name="appIntegrations" type="List&lt;com.sast.cis.core.data.PublicAppIntegrationData&gt;"/>
        <property name="currency" type="String"/>
        <property name="averageRating" type="Double"/>
        <property name="numberOfReviews" type="Integer"/>
        <property name="followAppData" type="com.sast.cis.core.data.FollowAppData"/>
        <property name="ownCompanyAppAndCanBuy" type="boolean"/>
        <property name="integratorCountryBlockedInDeveloperCountry" type="boolean"/>
        <property name="countryEulas" type="List&lt;com.sast.cis.core.data.CountryEulaData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.ProductContainerOverviewData">
        <property name="deletable" type="boolean"/>
        <property name="updated" type="java.util.Date"/>
        <property name="appApprovalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="latestVersionApprovalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="url" type="String"/>
        <property name="name" type="String"/>
        <property name="versionName" type="String"/>
        <property name="iconUrl" type="String"/>
        <property name="code" type="String"/>
        <property name="averageRating" type="Double"/>
        <property name="enabledInStore" type="boolean"/>
        <property name="appEditable" type="boolean">
            <description>Whether users are allowed to edit the App or App Draft in the console</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.ProductContainersData" extends="DevconData">
        <property name="productContainers" type="java.util.List&lt;ProductContainerOverviewData&gt;"/>
        <property name="appNameLimit" type="Long"/>
    </bean>

    <bean class="com.sast.cis.core.data.PermissionData">
        <property name="name" type="String"/>
        <property name="iconUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.DeviceCapabilityData">
        <property name="displayName" type="String"/>
        <property name="code" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.RuntimeData">
        <property name="code" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="description" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.BundleInfoData">
        <property name="code" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="size" type="int" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.ProductLicenseData">
        <property name="code" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="price" type="Double" equals="true"/>
        <property name="currencySymbol" type="String" equals="true"/>
        <property name="currencyIsocode" type="String" equals="true"/>
        <property name="licenseType" type="com.sast.cis.core.enums.LicenseType" equals="true"/>
        <property name="scalePrices" type="java.util.Set&lt;com.sast.cis.core.data.ScalePrice&gt;"/>
        <property name="futurePrices" type="java.util.Set&lt;com.sast.cis.core.data.PriceRowData&gt;"/>
        <property name="purchasability" type="com.sast.cis.core.data.LicensePurchasability" equals="true"/>
        <property name="runtime" type="com.sast.cis.core.data.RuntimeData" equals="true"/>
        <property name="bundleInfo" type="com.sast.cis.core.data.BundleInfoData" equals="true"/>
        <property name="specialOffer" type="boolean" equals="true"/>
    </bean>

    <enum class="com.sast.cis.core.data.LicensePurchasability">
        <value>PURCHASABLE</value><!-- License can be purchased -->
        <value>NOT_PUBLISHED</value><!-- license is not enabled by the developer -->
        <value>COUNTRY_DISABLED</value><!-- license is not available in the buyer country -->
        <value>TYPE_UNSUPPORTED</value><!-- type of this license is currently not supported by the platform -->
        <value>UNPAYABLE</value><!-- there is no possibility for the buyer to pay for the license -->
        <value>UNAVAILABLE</value><!-- license can not be purchased for an unspecified reason -->
        <value>COUNTRY_BLOCKED</value><!-- Integrator country is blocked in the App Developer's country -->
        <value>IMPORTED_COMPANY_NOT_ELIGIBLE</value><!-- Imported company is not allowed to make purchases -->
    </enum>

    <enum class="com.sast.cis.core.data.UserMessageLevel">
        <value>INFO</value>
        <value>WARNING</value>
        <value>ERROR</value>
    </enum>

    <bean class="com.sast.cis.core.data.SortDetailsData">
        <property name="requestedPage" type="int"/>
        <property name="sortCode" type="String"/>
        <property name="searchTerm" type="String"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.order.data.AbstractOrderData">
        <property name="continueShoppingUrl" type="String"/>
        <property name="paymentAddress" type="AddressData"/>
        <property name="integratorUserId" type="String"/>
        <property name="integratorAccountId" type="String"/>
        <property name="companyId" type="String"/>
        <property name="paymentInfoData" type="com.sast.cis.core.data.PaymentInfoData"/>
        <property name="ownAppsPurchase" type="boolean"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.order.data.OrderData">
        <property name="invoices" type="java.util.List&lt;com.sast.cis.core.data.InvoiceData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderPaymentUpdateData">
        <property name="order" type="com.sast.cis.core.data.OrderDetailsData"/>
        <property name="checkoutInfo" type="java.util.List&lt;com.sast.cis.core.data.CheckoutInfoData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.ApkStorageLocationData">
        <property name="path" type="String"/>
        <property name="versionId" type="String"/>
    </bean>

    <bean class="de.hybris.platform.commerceservices.service.data.CommerceCheckoutParameter">
        <property name="request" type="javax.servlet.http.HttpServletRequest"/>
    </bean>

    <bean class="com.sast.cis.core.data.AddToCartData">
        <property name="productCode" type="String"/>
        <property name="quantity" type="long"/>
        <property name="entryNumber" type="long"/>
        <property name="errorMsg" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseQuantityData">
        <property name="code" type="String"/>
        <property name="quantity" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicensePurchaseData">
        <property name="licenses" type="java.util.List&lt;com.sast.cis.core.data.LicenseQuantityData&gt;"/>
        <property name="specialOfferAccepted" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.ApkMetaData">
        <property name="packageName" type="String" equals="true"/>
        <property name="versionName" type="String" equals="true"/>
        <property name="versionCode" type="Long" equals="true"/>
        <property name="fileContent" type="byte[]" equals="true"/>
        <property name="permissions" type="java.util.List&lt;String&gt;" equals="true"/>
        <property name="deviceCapabilities" type="java.util.List&lt;String&gt;" equals="true"/>
        <property name="declaredPermissions" type="java.util.List&lt;String&gt;" equals="true"/>
        <property name="signatures" type="java.util.List&lt;com.sast.cis.core.data.ApkSignatureData&gt;" equals="true"/>
        <property name="sdkAddonVersion" type="Integer" equals="true"/>
        <property name="minAndroidApiVersion" type="Integer" equals="true"/>
        <property name="maxAndroidApiVersion" type="Integer" equals="true"/>
        <property name="targetAndroidApiVersion" type="Integer" equals="true"/>
        <property name="manifestXml" type="String" equals="true"/>
        <property name="debuggable" type="boolean" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.ApkSignatureData">
        <property name="certificateSha256" type="String" equals="true"/>
        <property name="signatureVersion" type="com.sast.cis.core.enums.SignatureVersion" equals="true"/>
        <property name="validTo" type="java.util.Date" equals="true"/>
        <property name="subjectCName" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.ErrorMessageData">
        <property name="message" type="String" equals="true"/>
        <property name="warning" type="boolean" equals="true"/>
        <property name="inputField" type="String" equals="true"/>
        <property name="userMessage" type="com.sast.cis.core.data.UserMessage" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.StoreErrorResponseData">
        <property name="userMessages" type="java.util.List&lt;com.sast.cis.core.data.UserMessage&gt;" equals="true"/>
        <property name="errorMessage" type="String" equals="true"/>
        <property name="errorCode" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.ProductContainerCreationData">
        <property name="title" type="String"/>
        <property name="errorMessage" type="String"/>
        <property name="redirectUrl" type="String"/>
        <property name="code" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.ReleasabilityStateData">
        <property name="publicListingReleasable" type="boolean"/>
        <property name="apkReleasable" type="boolean"/>
        <property name="pricingReleasable" type="boolean"/>
        <property name="availabilityReleasable" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.DevconData">
        <property name="entryPage" type="com.sast.cis.core.enums.DevconPage"/>
        <property name="errors" type="java.util.List&lt;ErrorMessageData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.ProductContainerRelatedData" extends="DevconData">
        <property name="productContainerCode" type="String"/>
        <property name="name" type="String"/>
        <property name="draft" type="boolean"/>
        <property name="approved" type="boolean"/>
        <property name="appEditable" type="boolean">
            <description>Whether users are allowed to edit the App or App Draft in the console</description>
        </property>
        <property name="draftRejectionReason" type="com.sast.cis.core.enums.RejectionCode"/>
        <property name="releasabilityState" type="com.sast.cis.core.data.ReleasabilityStateData"/>
        <property name="appIconUrl" type="String"/>
        <property name="appShopUrl" type="String"/>
        <property name="appApprovalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="latestVersionApprovalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="releaseUrl" type="String"/>
        <property name="postUrl" type="String"/>
        <property name="enabledInStoreSidebarFlag" type="boolean"/>
        <property name="disabledStoreLinkHint" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.RejectionReasonData">
        <property name="rejectionCode" type="com.sast.cis.core.enums.RejectionCode"/>
        <property name="comment" type="java.lang.String"/>
    </bean>

    <bean class="com.sast.cis.core.data.ClientImageData">
        <property name="url" type="String"/>
        <property name="qualifier" type="String">
            <description>Qualifier of the associated {@link de.hybris.platform.core.model.media.MediaContainerModel}.</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.PdfData">
        <property name="displayName" type="String"/>
        <property name="url" type="String"/>
        <property name="size" type="Long"/>
        <property name="code" type="String">
            <description>
                Qualifier of the associated {@link de.hybris.platform.bootstrap.gensrc.com.sast.cis.core.model.PdfMediaModel}.
            </description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.SelfBillingInvoiceData">
        <property name="displayName" type="String"/>
        <property name="url" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PublicListingFieldLengthData">
        <property name="fieldLengthDescription" type="int"/>
        <property name="fieldLengthSummary" type="int"/>
        <property name="fieldLengthName" type="int"/>
        <property name="screenshotsUploadLimit" type="int"/>
        <property name="documentsUploadLimit" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.PublicAppIntegrationData">
        <property name="displayName" type="String"/>
        <property name="externalDescription" type="String"/>
        <property name="integrationType" type="com.sast.cis.core.enums.StandardAppIntegrationType"/>
        <property name="type" type="com.sast.cis.core.enums.AppIntegrationType"/>
        <property name="documentation" type="com.sast.cis.core.data.PdfData"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppIntegrationData">
        <property name="code" type="String"/>
        <property name="name" type="String"/>
        <property name="description" type="String"/>
        <property name="status" type="com.sast.cis.core.enums.AppIntegrationStatus"/>
        <property name="type" type="com.sast.cis.core.enums.AppIntegrationType"/>
        <property name="documentation" type="com.sast.cis.core.data.PdfData"/>
        <property name="standardIntegrationCode" type="String"/>
        <property name="standardIntegrationOrder" type="Integer"/>
    </bean>


    <bean class="com.sast.cis.core.data.EulaData">
        <property name="type" type="com.sast.cis.core.enums.EulaType"/>
        <property name="customUrl" type="String"/>
        <property name="standardEulaAppendix" type="com.sast.cis.core.data.PdfData"/>
    </bean>

    <bean class="com.sast.cis.core.data.CountryEulaData">
        <description>EULA applicable for a specific country</description>
        <property name="label" type="String" equals="true"/>
        <property name="url" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.StoreContentData" extends="com.sast.cis.core.data.ProductContainerRelatedData">
        <property name="nameByIsocode" type="java.util.Map&lt;String,String&gt;"/>
        <property name="useCases" type="java.util.List&lt;com.sast.cis.core.data.UseCaseData&gt;"/>
        <property name="summaryByIsocode" type="java.util.Map&lt;String,String&gt;"/>
        <property name="descriptionByIsocode" type="java.util.Map&lt;String,String&gt;"/>
        <property name="icon" type="java.util.List&lt;com.sast.cis.core.data.ClientImageData&gt;"/>
        <property name="emailAddress" type="String"/>
        <property name="supportPhoneNumber" type="String"/>
        <property name="productWebsiteUrl" type="String"/>
        <property name="privacyPolicyUrl" type="String"/>
        <property name="supportPageUrl" type="String"/>
        <property name="eula" type="com.sast.cis.core.data.EulaData"/>
        <property name="screenshots" type="java.util.List&lt;com.sast.cis.core.data.ClientImageData&gt;"/>
        <property name="documentationFiles" type="java.util.List&lt;com.sast.cis.core.data.PdfData&gt;"/>
        <property name="fieldLengths" type="com.sast.cis.core.data.PublicListingFieldLengthData"/>
        <property name="industries" type="java.util.List&lt;com.sast.cis.core.data.IndustryData&gt;"/>
        <property name="videoUrl" type="String"/>
        <property name="videoStatus" type="com.sast.cis.core.enums.AppVideoStatus"/>
        <property name="appIntegrations" type="java.util.List&lt;com.sast.cis.core.data.AppIntegrationData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppVersionOverviewData">
        <property name="versionName" type="String"/>
        <property name="approvalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="code" type="String"/>
        <property name="lastModified" type="java.util.Date"/>
        <property name="packageName" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.VersionsOverviewData" extends="com.sast.cis.core.data.ProductContainerRelatedData">
        <property name="versions" type="java.util.List&lt;AppVersionOverviewData&gt;"/>
        <property name="apkMaxDisplaySize" type="String"/>
        <property name="redirectUrl" type="String"/>
        <property name="deleteUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppVersionMetaData" extends="com.sast.cis.core.data.ProductContainerRelatedData">
        <property name="appApk" type="String"/>
        <property name="changelogByIsocode" type="java.util.Map&lt;String,String&gt;"/>
        <property name="apkMetaData" type="com.sast.cis.core.data.ApkMetaData"/>
        <property name="approvalStatus" type="de.hybris.platform.catalog.enums.ArticleApprovalStatus"/>
        <property name="rejectionReasons" type="java.util.List&lt;com.sast.cis.core.data.RejectionReasonData&gt;"/>
        <property name="appVersionCode" type="String"/>
        <property name="isAppVersionDraft" type="boolean"/>
        <property name="saved" type="boolean"/>
        <property name="fieldLengthChangelog" type="int"/>
        <property name="exportRegulationAcknowledged" type="boolean"/>
        <property name="warnings" type="java.util.List&lt;com.sast.cis.core.data.ErrorMessageData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppAvailabilityData" extends="com.sast.cis.core.data.ProductContainerRelatedData">
        <property name="storeAvailabilityMode" type="com.sast.cis.core.enums.StoreAvailabilityMode">
            <description>Indicates the availability mode of the store listing</description>
        </property>
        <property name="countries" type="java.util.Set&lt;com.sast.cis.core.data.DevCountryData&gt;">
            <description>All countries supported by the marketplace</description>
        </property>
        <property name="proposed" type="boolean">
            <description>The availability is proposed as configured in the backend but not confirmed by the developer</description>
        </property>
        <property name="permittedBuyers" type="java.util.Set&lt;com.sast.cis.core.data.PermittedBuyerData&gt;">
            <description>Buyers that are permitted to buy licenses of this app</description>
        </property>
        <property name="newCountryAddingAllowed" type="boolean">
            <description>Developer consent to add new Azena countries to App enabled countries automatically</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.AppPricesData" extends="com.sast.cis.core.data.ProductContainerRelatedData">
        <property name="minimumCommissionWarningPrices" type="java.util.Map&lt;String,Double&gt;">
            <description>For each license type, the lowest price at which Azena's marketplace commission exceeds the minimum fee
            </description>
        </property>
        <property name="supportedCurrencies" type="java.util.Set&lt;String&gt;">
            <description>ISO 4217 codes of currencies available in the marketplace</description>
        </property>
        <property name="licenses" type="java.util.Set&lt;com.sast.cis.core.data.LicensePriceData&gt;">
            <description>Pricing data for all licenses of this app</description>
        </property>
        <property name="developerCurrency" type="String">
            <description>ISO 4217 code of the developer company currency</description>
        </property>
        <property name="proposed" type="boolean">
            <description>The prices are proposed as configured in the backend but not confirmed by the developer</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseBaseData">
        <property name="code" type="String">
            <description>Code of this license (is unique)</description>
        </property>
        <property name="type" type="com.sast.cis.core.enums.LicenseType">
            <description>Type of this license</description>
        </property>
        <property name="editable" type="boolean">
            <description>Indicates whether this license can be modified</description>
        </property>
        <property name="enabled" type="boolean">
            <description>Flag to enable/disable app license type.</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseAvailabilityData" extends="com.sast.cis.core.data.LicenseBaseData">
    </bean>

    <bean class="com.sast.cis.core.data.LicensePriceData" extends="com.sast.cis.core.data.LicenseBaseData">
        <property name="convertedPrices" type="java.util.Map&lt;String,String&gt;">
            <description>Converted prices in supported currencies based upon conversion rate (e.g from USD to EUR)</description>
        </property>
        <property name="existingPrices" type="java.util.Map&lt;String,String&gt;">
            <description>Existing converted prices stored in database</description>
        </property>
        <property name="specifiedPrice" type="String">
            <description>Price amount as specified by developer (in developer company currency)</description>
        </property>
        <property name="nextPriceUpdate" type="java.util.Date">
            <description>If present, indicates the start date of future prices.</description>
        </property>
        <property name="currentlyActivePrices" type="java.util.Map&lt;String,String&gt;">
            <description>Currently active price</description>
        </property>
        <property name="volumeDiscounts" type="java.util.List&lt;com.sast.cis.core.data.VolumeDiscountData&gt;">
            <description>Volume discounts of license, currently only one time purchase license supports volume discounts.</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.VolumeDiscountData">
        <property name="pk" type="Long"/>
        <property name="minQuantity" type="int"/>
        <property name="discount" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.PermittedBuyerData">
        <property name="companyId" type="String"/>
        <property name="companyName" type="String"/>
        <property name="country" type="com.sast.cis.core.data.DevCountryData"/>
    </bean>

    <bean class="com.sast.cis.core.data.ApkData">
        <property name="productContainerCode" type="String"/>
        <property name="apk" type="org.springframework.web.multipart.MultipartFile"/>
        <property name="parsedApk" type="com.sast.cis.core.data.ApkMetaData">
            <description>Should be filled before validating/persisting app. null is interpreted as missing/invalid APK.</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.UserMessage">
        <property name="level" type="com.sast.cis.core.data.UserMessageLevel" equals="true"/>
        <property name="code" type="String" equals="true"/>
        <property name="fallbackMessage" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.GlobalMessages">
        <property name="info" type="java.util.List&lt;String&gt;" deprecated="true"/>
        <property name="warning" type="java.util.List&lt;String&gt;" deprecated="true"/>
        <property name="error" type="java.util.List&lt;String&gt;" deprecated="true"/>
        <property name="messages" type="java.util.List&lt;UserMessage&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.CoreData">
        <property name="baseUrl" type="String"/>
        <property name="basestore" type="String"/>
        <property name="defaultLanguage" type="String"/>
        <property name="userName" type="String"/>
        <property name="csrfToken" type="String"/>
        <property name="currencySymbols" type="java.util.Map&lt;String,String&gt;"/>
        <property name="moduleConfig" type="java.util.Map&lt;String,Boolean&gt;"/>
        <property name="currentCurrency" type="String"/>
        <property name="globalMessages" type="GlobalMessages"/>
        <property name="domainUrl" type="String"/>
        <property name="httpStatus" type="int"/>
        <property name="navigationItems"
                  type="java.util.List&lt;NavigationItemData&gt;"
                  deprecated="Endpoint devcon-api/navigation-items is exposing navigation data"
        />
        <property name="supportUrl" type="String"/>
        <property name="contactUrl" type="String"/>
        <property name="infoUrl" type="String"/>
        <property name="corporateHome" type="String"/>
        <property name="accountsUrl" type="String"/>
        <property name="currentCountry" type="de.hybris.platform.commercefacades.user.data.CountryData"/>
        <property name="currentCompany" type="com.sast.cis.core.data.IotCompanyData"/>
        <property name="activeCountries" type="java.util.List&lt;de.hybris.platform.commercefacades.user.data.CountryData&gt;"/>
        <property name="idp" type="com.sast.cis.core.data.Idp"/>
    </bean>

    <bean class="com.sast.cis.core.data.Idp">
        <property name="loginIdp" type="String"/>
        <property name="loginIdpAccountUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.DevconCoreData" extends="com.sast.cis.core.data.CoreData">
        <property name="developerPortalUrl"
                  type="String"
                  deprecated="Endpoint devcon-api/navigation-items is exposing navigation data which contains URL"
        />
        <property name="payoutAccountValidated" type="boolean"/>
        <property name="allowedToSell" type="boolean"/>
        <property name="manualAppApprovalEnabled" type="boolean"/>
        <property name="apkMaxDisplaySize" type="String" deprecated="Moved to core VersionOverviewData as it is page specific"/>
    </bean>

    <bean class="com.sast.cis.core.data.ShopCoreData" extends="com.sast.cis.core.data.CoreData">
        <property name="numberOfCartItems" type="Integer"/>
        <property name="thlAppCodeInCart" type="String"/>
        <property name="camerasUrl" type="String"/>
        <property name="myAppsUrl" type="String"/>
        <property name="allowedToBuy" type="boolean"/>
        <property name="userCompanyUnderReview" type="boolean"/>
        <property name="allowedMaxQuantityLineItemFullLicense" type="Long"/>
        <property name="allowedMaxQuantityLineItemSubsLicense" type="Long"/>
        <property name="globalDefaultLanguage" type="String"/>
        <property name="globalFallbackCountry" type="String"/>
        <property name="guideCustomerToProvideDirectDebitMandate" type="boolean"/>
        <property name="purchaseDisabledBannerInfo" type="com.sast.cis.core.data.ImportedCompanyPurchaseEligibilityInfoDto"/>
        <property name="translationConfig" type="com.sast.cis.core.data.TranslationConfig"/>
    </bean>

    <bean class="com.sast.cis.core.data.TranslationConfig">
        <property name="remoteTranslationsEnabled" type="boolean"/>
        <property name="baseUrl" type="String"/>
        <property name="projectId" type="String"/>
        <property name="cdsProjectId" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.MediaUploadData">
        <property name="persistedMedia" type="java.util.List&lt;ClientImageData&gt;"/>
        <property name="uploadErrorCode" type="java.lang.String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PdfUploadData">
        <property name="persistedDocuments" type="java.util.List&lt;PdfData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.SidebarLinkData">
        <property name="subpageUrl" type="String"/>
        <property name="isActivePage" type="Boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.UmpUserData">
        <property name="userName" type="String"/>
        <property name="email" type="String"/>
        <property name="firstName" type="String"/>
        <property name="lastName" type="String"/>
        <property name="companyName" type="String"/>
        <property name="companyId" type="String"/>
        <property name="activated" type="boolean"/>
        <property name="isHardwarePartner" type="boolean"/>
        <property name="clientSpecificRoles" type="java.util.List&lt;String&gt;"/>
        <property name="communicationLanguage" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.UmpCompanyData">
        <property name="companyId" type="String"/>
        <property name="companyName" type="String"/>
        <property name="friendlyName" type="String"/>
        <property name="taxId" type="String"/>
        <property name="companyCountry" type="String"/>
        <property name="companyStatus" type="String"/>
        <property name="isHardwarePartner" type="boolean"/>
        <property name="businessAddress" type="com.sast.cis.core.data.UmpAddressData"/>
        <property name="billingAddress" type="com.sast.cis.core.data.UmpAddressData"/>
        <property name="companyTypes" type="java.util.Set&lt;UmpCompanyType&gt;"/>
        <property name="creditLimit" type="java.math.BigDecimal"/>
        <property name="bpmdId" type="String"/>
        <property name="costCenter" type="String"/>
        <property name="sellerEmails" type="java.util.Set&lt;String&gt;"/>
        <property name="isManualAppApprovalEnabled" type="Boolean"/>
        <property name="isOwnAppsPurchaseEnabled" type="boolean"/>
        <property name="marketplaceId" type="String"/>
        <property name="externalCustomerId" type="String"/>
        <property name="distributor" type="com.sast.cis.core.data.UmpDistributorData"/>
        <property name="isManaged" type="boolean"/>
        <property name="licensingEmail" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.UmpDistributorData">
        <property name="id" type="String"/>
        <property name="name" type="String"/>
        <property name="externalCustomerId" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.UmpDistributorHistoryData">
        <property name="id" type="String"/>
        <property name="distributor" type="com.sast.cis.core.data.UmpDistributorData"/>
        <property name="creationDate" type="java.util.Date"/>
    </bean>

    <enum class="com.sast.cis.core.data.UmpCompanyType">
        <value>INTEGRATOR</value>
        <value>DEVELOPER</value>
        <value>MANUFACTURER</value>
    </enum>

    <bean class="com.sast.cis.core.data.UmpAddressData">
        <property name="city" type="String"/>
        <property name="postalCode" type="String"/>
        <property name="street" type="String"/>
        <property name="houseNumber" type="String"/>
        <property name="state" type="String"/>
        <property name="region" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.DatedValue">
        <property name="date" type="java.util.Date"/>
        <property name="quantity" type="long"/>
    </bean>

    <bean class="com.sast.cis.core.data.PriceData">
        <property name="symbol" type="String"/>
        <property name="value" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PriceRowData">
        <property name="currencyIsoCode" type="String"/>
        <property name="price" type="String"/>
        <property name="startDate" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.ScalePriceInfo" extends="de.hybris.platform.commercefacades.product.data.PriceData"/>

    <bean class="com.sast.cis.core.data.CartItemData">
        <property name="appCode" type="String"/>
        <property name="productName" type="String"/>
        <property name="productCode" type="String"/>
        <property name="productUrl" type="String"/>
        <property name="companyName" type="String"/>
        <property name="versionName" type="String"/>
        <property name="licenseName" type="String"/>
        <property name="licenseType" type="String"/>
        <property name="logoUrl" type="String"/>
        <property name="smallLogoUrl" type="String"/>
        <property name="itemPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="productFuturePrices" type="java.util.Set&lt;com.sast.cis.core.data.PriceData&gt;"/>
        <property name="quantity" type="long"/>
        <property name="entryNumber" type="long"/>
        <property name="scalePrices" type="java.util.List&lt;com.sast.cis.core.data.ScalePriceInfo&gt;"/>
        <property name="bundleInfo" type="com.sast.cis.core.data.BundleInfoData"/>
        <property name="sellerProductId" type="String"/>
        <property name="countryEulas" type="List&lt;com.sast.cis.core.data.CountryEulaData&gt;"/>
        <property name="runtime" type="com.sast.cis.core.data.RuntimeData"/>
        <property name="specialOffer" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.CartPageData">
        <property name="totalPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalPriceWithTax" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalTax" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalPriceWithoutDiscount" type="com.sast.cis.core.data.PriceData"/>
        <property name="cartItems" type="java.util.List&lt;CartItemData&gt;"/>
        <property name="cartHash" type="String"/>
        <property name="cartCode" type="String"/>
        <property name="developerCompanyName" type="String"/>
        <property name="ownAppsPurchase" type="boolean"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.ReviewData">
        <property name="showCompany" type="boolean"/>
        <property name="showName" type="boolean"/>
        <property name="company" type="String"/>
    </bean>

    <enum class="com.sast.cis.core.data.ReviewStatus">
        <value>NOT_BOUGHT_APP</value>
        <value>READY_FOR_REVIEW</value>
        <value>REVIEW_IS_ALREADY_DONE</value>
    </enum>

    <bean class="com.sast.cis.core.data.OrderSummaryData">
        <property name="paymentAddress" type="de.hybris.platform.commercefacades.user.data.AddressData"/>
        <property name="totalPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalTax" type="com.sast.cis.core.data.PriceData"/>
        <property name="totalPriceWithTax" type="com.sast.cis.core.data.PriceData"/>
        <property name="entries" type="java.util.List&lt;com.sast.cis.core.data.CartItemData&gt;"/>
        <property name="paymentInfo" type="com.sast.cis.core.data.PaymentInfoData"/>
        <property name="paymentMethod" type="com.sast.cis.core.enums.PaymentMethodType"/>
        <property name="cartHash" type="String"/>
        <property name="showInvoiceNotes" type="boolean"/>
        <property name="invoiceNoteSizeLimit" type="Integer"/>
        <property name="ownAppsPurchase" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderDetailsData" extends="com.sast.cis.core.data.OrderSummaryData">
        <property name="placedBy" type="String"/>
        <property name="code" type="String"/>
        <property name="status" type="String"/>
        <property name="failedPayment" type="com.sast.cis.core.data.FailedPaymentData"/>
        <property name="date" type="java.util.Date"/>
        <property name="invoices" type="java.util.List&lt;com.sast.cis.core.data.InvoiceData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.FailedPaymentData">
        <property name="failedPaymentInvoiceId" type="String"/>
        <property name="cardNumber" type="String"/>
        <property name="updateUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.CoreInvoicingDocumentData">
        <property name="externalId" type="String"/>
        <property name="documentUrl" type="String"/>
        <property name="displayName" type="String"/>
        <property name="netAmount" type="com.sast.cis.core.data.PriceData"/>
        <property name="taxAmount" type="com.sast.cis.core.data.PriceData"/>
        <property name="grossAmount" type="com.sast.cis.core.data.PriceData"/>
        <property name="creationTime" type="java.util.Date"/>
    </bean>

    <bean class="com.sast.cis.core.data.InvoiceCreditNoteData" extends="com.sast.cis.core.data.CoreInvoicingDocumentData">
        <property name="creditNoteType" type="String"/>
        <property name="issuanceDate" type="java.util.Date"/>
    </bean>

    <bean class="com.sast.cis.core.data.InvoiceData" extends="com.sast.cis.core.data.CoreInvoicingDocumentData">
        <property name="invoiceStatus" type="String"/>
        <property name="invoiceDate" type="java.util.Date"/>
        <property name="creditNotes" type="java.util.List&lt;com.sast.cis.core.data.InvoiceCreditNoteData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderPaymentData" extends="com.sast.cis.core.data.OrderSummaryData">
        <property name="checkoutInfos" type="java.util.List&lt;com.sast.cis.core.data.CheckoutInfoData&gt;"/>
        <property name="confirmationPending" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.CheckoutInfoData">
        <property name="paymentInfos" type="java.util.List&lt;com.sast.cis.core.data.PaymentInfoData&gt;"/>
        <property name="paymentProvider" type="com.sast.cis.core.enums.PaymentProvider"/>
        <property name="paymentMethod" type="com.sast.cis.core.enums.PaymentMethodType"/>
        <property name="userActionParameters" type="java.util.Map&lt;String,String&gt;"/>
        <property name="userCreatable" type="boolean"/>
        <property name="savableForReuse" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.PlaceOrderData">
        <property name="cartHash" type="String"/>
        <property name="paymentIntentId" type="String"/>
        <property name="invoiceNotes" type="java.util.List&lt;String&gt;"/>
        <property name="eulaAcceptanceData" type="com.sast.cis.core.data.EulaAcceptanceData"/>
        <property name="distributorID" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.EulaAcceptanceData">
        <property name="eulaAccepted" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.MaskedCardData">
        <property name="cardTypeDisplayName" type="String"/>
        <property name="cardType" type="String"/>
        <property name="cardNumber" type="String"/>
        <property name="expiryMonth" type="String"/>
        <property name="expiryYear" type="String"/>
        <property name="accountHolderName" type="String"/>
        <property name="paymentMethodId" type="String"/>
        <property name="isSaved" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.GalleryItemData">
        <property name="url" type="String"/>
        <property name="key" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.FacetData">
        <property name="group" type="String"/>
        <property name="orderIndex" type="int"/>
        <property name="items" type="java.util.List&lt;com.sast.cis.core.data.FacetItem&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.FacetItem">
        <property name="name" type="String"/>
        <property name="facetIndex" type="String"/>
        <property name="facetValue" type="String"/>
        <property name="count" type="Long"/>
    </bean>

    <bean class="com.sast.cis.core.data.CategoryPageData">
        <property name="totalNumberOfResults" type="long"/>
        <property name="pageNumber" type="int"/>
        <property name="totalNumberOfPages" type="int"/>
        <property name="pageSize" type="int"/>
        <property name="searchQuery" type="String"/>
        <property name="products" type="java.util.List&lt;com.sast.cis.core.data.SimpleProductData&gt;"/>
        <property name="facets" type="java.util.List&lt;com.sast.cis.core.data.FacetData&gt;"/>
    </bean>

    <enum class="de.hybris.platform.payment.dto.TransactionStatus">
        <value>PENDING_REVOCATION</value>
        <value>REVOKED</value>
        <value>PENDING</value>
    </enum>

    <enum class="de.hybris.platform.payment.dto.TransactionStatusDetails">
        <value>REJECTED_BY_PSP</value>
    </enum>

    <bean class="de.hybris.platform.commercefacades.user.data.CountryData">
        <property name="canBuy" type="boolean"/>
        <property name="blockedCountriesCommercial" type="java.util.Set&lt;String&gt;"/>
        <property name="supportedPaymentMethodTypes" type="java.util.Set&lt;com.sast.cis.core.enums.PaymentMethodType&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.DevCountryData" extends="de.hybris.platform.commercefacades.user.data.CountryData">
        <property name="currencyIsoCode" type="String"/>
        <property name="sepaEnabled" type="boolean"/>
        <property name="selected" type="boolean"/>
    </bean>

    <enum class="com.sast.cis.core.data.PayoutType">
        <value>DEBIT</value>
        <value>BANK</value>
    </enum>

    <enum class="com.sast.cis.core.data.BankAccountType">
        <value>IBAN</value>
        <value>BSB</value>
        <value>CA_BANK</value>
        <value>SG_BANK</value>
        <value>US_BANK</value>
    </enum>

    <bean class="com.sast.cis.core.data.MaskedPayoutMethod">
        <property name="type" type="PayoutType"/>
        <property name="name" type="String"/>
        <property name="last4" type="String"/>
        <property name="vendor" type="String"/>
        <property name="cardExpiryMonth" type="Integer"/>
        <property name="cardExpiryYear" type="Integer"/>
    </bean>

    <enum class="com.sast.cis.core.data.StripeVerificationStatus">
        <value>PENDING</value>
        <value>VERIFIED</value>
        <value>INFORMATION_MISSING</value>
    </enum>

    <enum class="com.sast.cis.core.data.StripeProgressStatus">
        <value>ACCOUNT_CREATED</value>
        <value>INFORMATION_PROVIDED</value>
        <value>PAYOUT_PROVIDED</value>
    </enum>

    <enum class="com.sast.cis.core.data.AccountStatus">
        <value>IN_PROGRESS</value>
        <value>COMPLETED</value>
        <value>INVALIDATED</value>
    </enum>

    <bean class="com.sast.cis.core.data.PayoutAccountData">
        <property name="contactEmail" type="String"/>
        <property name="allowedPayoutMethod" type="PayoutType"/>
        <property name="connectAccountId" type="String" deprecated="Use generic accountId attribute instead"/>
        <property name="payoutMethod" type="MaskedPayoutMethod"/>
        <property name="createAccountUrl" type="String"/>
        <property name="companyName" type="String"/>
        <property name="companyCurrency" type="String"/>
        <property name="companyCountry" type="String"/>
        <property name="bankAccountType" type="BankAccountType"/>
        <property name="payoutFormUrl" type="String"/>
        <property name="status" type="AccountStatus" deprecated="Use generic accountStatus attribute instead"/>
        <property name="active" type="boolean" deprecated="Use generic accountStatus attribute instead"/>
        <property name="accountId" type="String"/>
        <property name="paymentProvider" type="com.sast.cis.core.enums.PaymentProvider"/>
        <property name="accountStatus" type="com.sast.cis.core.enums.PspSellerAccountStatus"/>
    </bean>

    <bean class="com.sast.cis.core.data.PayoutData">
        <property name="amount" type="com.sast.cis.core.data.PriceData"/>
        <property name="date" type="java.util.Date"/>
        <property name="paymentProvider" type="com.sast.cis.core.enums.PaymentProvider"/>
    </bean>

    <bean class="com.sast.cis.core.data.PayoutPageData" extends="DevconData">
        <property name="stripePayoutPageData" type="com.sast.cis.payment.stripe.data.StripePayoutPageData"/>
        <property name="dpgPayoutPageData" type="com.sast.cis.payment.dpg.data.DpgPayoutPageData"/>
    </bean>
    <!-- Order Management* beans are deprecated will be removed -->
    <bean class="com.sast.cis.core.data.OrderManagementAppData">
        <property name="name" type="String"/>
        <property name="type" type="String"/>
        <property name="iconUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderManagementItemData">
        <property name="orderId" type="String"/>
        <property name="date" type="java.util.Date"/>
        <property name="customer" type="String"/>
        <property name="apps" type="java.util.List&lt;com.sast.cis.core.data.OrderManagementAppData&gt;"/>
        <property name="totalPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="invoiceUrl" type="String"/>
        <property name="invoiceId" type="String"/>
        <property name="paymentStatus" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderManagementData" extends="DevconData">
        <property name="orders" type="java.util.List&lt;com.sast.cis.core.data.OrderManagementItemData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.OsCompatibilityData">
        <property name="minOsVersion" type="String"/>
        <property name="maxOsVersion" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.ExportInformationEntryData">
        <property name="appName" type="String"/>
        <property name="companyName" type="String"/>
        <property name="eccn" type="String"/>
        <property name="link" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.ExportInformationOverviewData">
        <property name="products" type="java.util.List&lt;ExportInformationEntryData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.NavigationItemData">
        <property name="id" type="String"/>
        <property name="url" type="String"/>
        <property name="type" type="String"/>
        <property name="group" type="String"/>
        <property name="text" type="String"/>
        <property name="description" type="String"/>
        <property name="target" type="String"/>
        <property name="icon" type="String"/>
        <property name="index" type="int"/>
        <property name="entryPage" type="String"/>
        <property name="itemCode" type="String"/>
        <property name="customAttributes" type="java.util.Map&lt;String,String&gt;"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.order.data.OrderHistoryData">
        <property name="placedBy" type="String"/>
        <property name="lastUpdated" type="java.util.Date"/>
        <property name="appIconUrls" type="java.util.List&lt;String>"/>
        <property name="paymentStatus" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.UrlData">
        <property name="code" type="String"/>
        <property name="url" type="String"/>
        <property name="sepaPaymentUsed" type="boolean"/>
        <property name="ownAppOrder" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.UseCaseData">
        <property name="id" type="long"/>
        <property name="name" type="String"/>
        <property name="index" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.PurchasedProductData">
        <property name="name" type="String"/>
        <property name="code" type="String"/>
        <property name="url" type="String"/>
        <property name="licenseType" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.CheckoutConfirmationData">
        <property name="urlData" type="java.util.List&lt;UrlData&gt;"/>
        <property name="camerasUrl" type="String"/>
        <property name="purchasedProducts" type="java.util.List&lt;PurchasedProductData&gt;"/>
        <property name="licensePortalUrl" type="String"/>
        <property name="licensingEmail" type="String"/>
        <property name="buyerEmail" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.IndustryData">
        <property name="id" type="Long"/>
        <property name="name" type="String"/>
        <property name="index" type="int"/>
    </bean>

    <bean class="com.sast.cis.core.data.AppVideoData">
        <property name="source" type="String"/>
        <property name="type" type="com.sast.cis.core.enums.AppVideoType" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.MiniCartData">
        <property name="totalQuantity" type="int"/>
        <property name="totalItems" type="int"/>
        <property name="totalCarts" type="int"/>
    </bean>
    <bean class="com.sast.cis.core.search.data.CisSearchQueryData" extends="de.hybris.platform.commercefacades.search.data.SearchQueryData">
        <property name="filterTerms"
                  type="java.util.List&lt;de.hybris.platform.commerceservices.search.solrfacetsearch.data.SolrSearchQueryTermData>"/>
    </bean>

    <bean class="com.sast.cis.core.data.PaymentInfoData">
        <property name="id" type="String"/>
        <property name="paymentMethod" type="com.sast.cis.core.enums.PaymentMethodType"/>
        <property name="saved" type="boolean"/>
        <property name="reusable" type="boolean"/>
        <property name="defaultPaymentInfo" type="boolean"/>
        <property name="paymentProvider" type="com.sast.cis.core.enums.PaymentProvider"/>
        <property name="companyScope" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.CreditCardPaymentInfoData" extends="com.sast.cis.core.data.PaymentInfoData">
        <property name="accountHolderName" type="String"/>
        <property name="cardType" type="String"/>
        <property name="cardTypeData" type="de.hybris.platform.commercefacades.order.data.CardTypeData"/>
        <property name="cardNumber" type="String"/>
        <property name="startMonth" type="String"/>
        <property name="startYear" type="String"/>
        <property name="expiryMonth" type="String"/>
        <property name="expiryYear" type="String"/>
        <property name="issueNumber" type="String"/>
        <property name="subscriptionId" type="String"/>
        <property name="billingAddress" type="de.hybris.platform.commercefacades.user.data.AddressData"/>
    </bean>

    <bean class="com.sast.cis.core.data.CreditCardPaymentInfoDatas">
        <property name="paymentInfos"
                  type="java.util.List&lt;com.sast.cis.core.data.CreditCardPaymentInfoData>"/>
    </bean>

    <bean class="com.sast.cis.core.data.InvoicePaymentInfoData" extends="com.sast.cis.core.data.PaymentInfoData">
        <property name="enabled" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.InvoicePaymentInfoDatas">
        <property name="paymentInfos"
                  type="java.util.List&lt;com.sast.cis.core.data.InvoicePaymentInfoData>"/>
    </bean>

    <bean class="com.sast.cis.core.data.DirectDebitPaymentInfoData" extends="com.sast.cis.core.data.PaymentInfoData">
    </bean>

    <bean class="com.sast.cis.core.data.SepaMandatePaymentInfoData" extends="com.sast.cis.core.data.DirectDebitPaymentInfoData">
        <property name="mandateReference" type="String"/>
        <property name="iban" type="String"/>
        <property name="accountHolderName" type="String"/>
        <property name="dateOfSignature" type="java.util.Date"/>
    </bean>

    <bean class="com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData"
          extends="com.sast.cis.core.data.SepaMandatePaymentInfoData">
        <property name="shopperReference" type="String"/>
        <property name="recurringReference" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PaymentSettingsData">
        <property name="ccPaymentInfos" type="java.util.List&lt;com.sast.cis.core.data.CreditCardPaymentInfoData>"/>
        <property name="invoicePaymentInfos" type="java.util.List&lt;com.sast.cis.core.data.InvoicePaymentInfoData>"/>
        <property name="directDebitPaymentInfos" type="java.util.List&lt;com.sast.cis.core.data.SepaMandatePaymentInfoData>"/>
        <property name="pendingPaymentInfoDrafts" type="java.util.List&lt;com.sast.cis.core.data.PaymentInfoDraftData>"/>
    </bean>

    <bean class="com.sast.cis.core.data.SepaCreditTransferPaymentInfoData" extends="com.sast.cis.core.data.InvoicePaymentInfoData">
        <property name="disableReason" type="String"/>
        <property name="accountHolder" type="String"/>
        <property name="iban" type="String"/>
        <property name="bankName" type="String"/>
        <property name="bic" type="String"/>
    </bean>
    <bean class="com.sast.cis.core.data.AchCreditTransferPaymentInfoData" extends="com.sast.cis.core.data.InvoicePaymentInfoData">
        <property name="accountHolder" type="String"/>
        <property name="accountNumber" type="String"/>
        <property name="routingNumber" type="String"/>
        <property name="bankName" type="String"/>
        <property name="bic" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PaymentInfoDraftData">
        <property name="code" type="String"/>
        <property name="paymentMethodType" type="com.sast.cis.core.enums.PaymentMethodType"/>
        <property name="paymentProvider" type="com.sast.cis.core.enums.PaymentProvider"/>
        <property name="creationStatus" type="com.sast.cis.core.enums.PaymentInfoDraftCreationStatus"/>
    </bean>

    <bean class="com.sast.cis.core.data.ScalePrice">
        <property name="minQuantity" type="int"/>
        <property name="discountPercent" type="int"/>
        <property name="discountPrice" type="double"/>
        <property name="currencyIsoCode" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.MasterCountryData">
        <property name="isoCode" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="canBuy" type="boolean" equals="true"/>
        <property name="activeInStore" type="boolean" equals="true"/>
        <property name="canSell" type="boolean" equals="true"/>
        <property name="paymentProviders" type="java.util.Set&lt;String&gt;" equals="true"/>
        <property name="paymentMethods" type="java.util.Set&lt;String&gt;" equals="true"/>
        <property name="blockedCountriesCommercial" type="java.util.Set&lt;String&gt;" equals="true"/>
        <property name="inEU" type="boolean" equals="true"/>
        <property name="tenantConfigurations" type="java.util.Set&lt;com.sast.cis.core.data.CountryTenantConfiguration&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.StoreMasterCountryData" extends="com.sast.cis.core.data.MasterCountryData" superEquals="true">
        <property name="languages" type="java.util.Set&lt;String&gt;"/>
        <property name="defaultLanguage" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.CountryTenantConfiguration">
        <property name="isoCode" type="String" equals="true"/>
        <property name="currency" type="String" equals="true"/>
        <property name="tenant" type="String" equals="true"/>
        <property name="storefrontEnabled" type="boolean" equals="true"/>
        <property name="languages" type="java.util.Set&lt;String&gt;" equals="true"/>
        <property name="defaultLanguage" type="String" equals="true"/>
        <property name="importedCompaniesCanBuy" type="boolean" equals="true"/>
    </bean>

    <bean class="com.sast.cis.core.data.PrivateOfferProjectAddressData">
        <property name="country" type="de.hybris.platform.commercefacades.user.data.CountryData"/>
        <property name="city" type="String"/>
        <property name="postalCode" type="String"/>
        <property name="line1" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.PrivateOfferProjectRegistrationData">
        <property name="projectName" type="String"/>
        <property name="customerName" type="String"/>
        <property name="plannedStartDate" type="String"/>
        <property name="siteAddress" type="PrivateOfferProjectAddressData"/>
    </bean>

    <bean class="com.sast.cis.core.data.PrivateOfferRequestItemData">
        <property name="originalPrice" type="java.math.BigDecimal"/>
        <property name="requestPrice" type="java.math.BigDecimal"/>
        <property name="quantity" type="Integer"/>
        <property name="licenseType" type="com.sast.cis.core.enums.LicenseType"/>
    </bean>

    <bean class="com.sast.cis.core.data.PrivateOfferRequestData">
        <property name="appCode" type="String"/>
        <property name="privateOfferRequestItems" type="java.util.List&lt;com.sast.cis.core.data.PrivateOfferRequestItemData>"/>
        <property name="messageText" type="String"/>
        <property name="registerProject" type="boolean"/>
        <property name="projectRegistration" type="PrivateOfferProjectRegistrationData"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseActivationData">
        <property name="companyId" type="String"/>
        <property name="licenseCode" type="String"/>
        <property name="activationStatus" type="com.sast.cis.core.enums.LicenseActivationStatus"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseActivationRequest">
        <property name="activationStatus" type="com.sast.cis.core.enums.LicenseActivationStatus"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseActivationMessage">
        <description>Message sent to DMP upon change in License activation status for a Company</description>
        <hints>
            <hint name="wsRelated"/>
        </hints>
        <import type="com.fasterxml.jackson.annotation.JsonProperty"/>
        <property name="companyAccountId" type="String"/>
        <property name="creationTimestamp" type="java.util.Date"/>
        <property name="transactionId" type="String"/>
        <property name="userId" type="String"/>
        <property name="application" type="LicenseActivationApplicationDetails"/>
    </bean>

    <bean class="com.sast.cis.core.data.LicenseActivationApplicationDetails">
        <description>Part of LicenseActivationMessage. Contains information about the License and App.</description>
        <hints>
            <hint name="wsRelated"/>
        </hints>
        <import type="com.fasterxml.jackson.annotation.JsonProperty"/>
        <property name="applicationId" type="String"/>
        <property name="versionName" type="String"/>
        <property name="versionCode" type="Long"/>
        <property name="enabled" type="boolean"/>
        <property name="licenseType" type="String"/>
    </bean>
    <bean class="com.sast.cis.core.data.FollowAppData">
        <description>Part of Detail Product data. contains integrator subscriptions to App Data changes</description>
        <property name="subscribed" type="boolean"/>
    </bean>
    <bean class="com.sast.cis.core.data.FollowAppSubscriptionRequest">
        <property name="followApp" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.core.data.BasicAppData">
        <property name="name" type="String"/>
        <property name="type" type="String"/>
        <property name="iconUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.SBIData">
        <property name="orderId" type="String"/>
        <property name="date" type="java.util.Date"/>
        <property name="customer" type="String"/>
        <property name="apps" type="java.util.List&lt;com.sast.cis.core.data.BasicAppData&gt;"/>
        <property name="totalPrice" type="com.sast.cis.core.data.PriceData"/>
        <property name="invoiceUrl" type="String"/>
        <property name="invoiceId" type="String"/>
        <property name="paymentStatus" type="String"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.PriceData">
        <property name="startDate" type="String"/>
        <property name="priceFrequency" type="com.sast.cis.core.enums.PriceFrequency"/>
    </bean>
    <bean class="de.hybris.platform.commercefacades.order.data.OrderEntryData">
        <property name="futurePrices" type="java.util.Set&lt;de.hybris.platform.commercefacades.product.data.PriceData&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.OrderPaymentUpdate">
        <property name="paymentInfoId" type="String" equals="true"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.catalog.data.CategoryHierarchyData"
          extends="de.hybris.platform.commercefacades.catalog.data.AbstractCatalogItemData">
        <property name="order" type="Integer"/>
        <property name="picture" type="de.hybris.platform.cmsfacades.data.MediaData"/>
        <property name="thumbnail" type="de.hybris.platform.cmsfacades.data.MediaData"/>
        <property name="iconCode" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.category.data.CategoryHierarchyDTO" extends="com.sast.cis.core.category.data.CategoryDTO">
        <description>Representation of a Category Hierarchy for Product Overview Page</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">CategoryHierarchyData</hint>
        </hints>
        <property name="subcategories"
                  type="java.util.List&lt;com.sast.cis.core.category.data.CategoryHierarchyDTO>">
            <description>List of subcategory hierarchies</description>
        </property>
        <property name="products"
                  type="java.util.List&lt;com.sast.cis.core.category.data.ProductDTO>"/>
    </bean>
    <bean class="com.sast.cis.core.category.data.PriceDTO">
        <description>Representation of a Price</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Price</hint>
        </hints>
        <property name="currencyIso" type="String">
            <description>Currency iso format</description>
        </property>
        <property name="value" type="java.math.BigDecimal">
            <description>Value of price in BigDecimal format</description>
        </property>
        <property name="formattedValue" type="String">
            <description>Value of price formatted</description>
        </property>
        <property name="priceFrequency" type="com.sast.cis.core.enums.PriceFrequency"/>
    </bean>
    <bean class="com.sast.cis.core.category.data.ProductDTO">
        <description>Representation of a Product for Category Page</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Product</hint>
        </hints>
        <property name="code" type="String">
            <description>Code of the product</description>
        </property>
        <property name="name" type="String">
            <description>Name of the product</description>
        </property>
        <property name="url" type="String">
            <description>Url address of the product</description>
        </property>
        <property name="description" type="String">
            <description>Description of the product</description>
        </property>
        <property name="summary" type="String">
            <description>Product summary</description>
        </property>
        <property name="minPrice" type="com.sast.cis.core.category.data.PriceDTO">
            <description>Min Price of the product</description>
        </property>
        <property name="features"
                  type="java.util.Collection&lt;com.sast.cis.core.category.data.FeatureDTO&gt;">
            <description>List of features for given classification</description>
        </property>
    </bean>
    <bean class="com.sast.cis.core.category.data.CategoryDTO">
        <description>Representation of a Category</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Category</hint>
        </hints>
        <property name="code" type="String">
            <description>Code of the category</description>
        </property>
        <property name="name" type="String">
            <description>Name of the category</description>
        </property>
        <property name="order" type="Integer">
            <description>Sort Order of the Category with respect to the sibling category.</description>
        </property>
        <property name="picture" type="com.sast.cis.core.category.data.ImageDTO">
            <description>Category picture</description>
        </property>
        <property name="thumbnail" type="com.sast.cis.core.category.data.ImageDTO">
            <description>Category thumbnail</description>
        </property>
        <property name="iconCode" type="String">
            <description>Category icon code</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.category.data.ImageDTO">
        <description>Representation of an Image</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Image</hint>
        </hints>
        <property name="url" type="String">
            <description>URL address of the image</description>
        </property>
        <property name="altText" type="String">
            <description>Tooltip content which is visible while image mouse hovering</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.category.data.FeatureDTO">
        <description>Representation of a Feature</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Feature</hint>
        </hints>
        <property name="code" type="String">
            <description>Code of the feature</description>
        </property>
        <property name="name" type="String">
            <description>Name of the feature</description>
        </property>
        <property name="featureValues"
                  type="java.util.Collection&lt;com.sast.cis.core.category.data.FeatureValueDTO>">
            <description>List of feature values</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.category.data.FeatureValueDTO">
        <description>Representation of a Feature Value</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">FeatureValue</hint>
        </hints>
        <property name="value" type="String">
            <description>Value of the feature</description>
        </property>
        <property name="iconUrl" type="String">
            <description>Feature value icon url</description>
        </property>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.FeatureValueData">
        <property name="iconUrl" type="String">
            <description>Feature value icon url</description>
        </property>
    </bean>

    <bean class="com.sast.cis.core.data.ProductPriceDTO">
        <property name="productCode" type="String"/>
        <property name="minPrice" type="de.hybris.platform.commercefacades.product.data.PriceData"/>
        <property name="specialOffer" type="boolean"/>
    </bean>

    <bean class="com.sast.cis.shop.frontend.i18n.CountryAndLanguageData">
        <property name="countryIsocode" type="java.lang.String"/>
        <property name="languageIsocode" type="java.lang.String"/>
    </bean>


    <bean class="com.sast.cis.core.data.PromotionPriceDto">
        <property name="licenseCode" type="String"/>
        <property name="price" type="java.math.BigDecimal"/>
        <property name="discountPrice" type="java.math.BigDecimal"/>
    </bean>

    <bean class="com.sast.cis.core.data.PromotionConsentDto">
        <property name="master" type="com.sast.cis.core.data.PromotionPriceDto"/>
        <property name="thl" type="com.sast.cis.core.data.PromotionPriceDto"/>
    </bean>

    <bean class="com.sast.cis.core.data.ImportedCompanyPurchaseEligibilityInfoDto">
        <property name="showPurchaseDisabledBanner" type="boolean"/>
        <property name="date" type="java.util.Date"/>
    </bean>
    <bean class="com.sast.cis.core.data.OrderCancelDto">
        <property name="orderNumber" type="String"/>
        <property name="gracePeriod" type="String"/>
        <property name="endDate" type="java.util.Date"/>
    </bean>
</beans>
