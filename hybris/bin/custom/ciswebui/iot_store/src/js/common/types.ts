import type {FollowAppData, ProductLicenseData} from 'common/generated-types/types';
import {ProductPriceDTO} from 'common/generated-types/types';

export interface TestScenario {
    coreData: any;
    pageData: any;
    url: string;
}

export interface TestMap { [key: string]: TestScenario; }

export interface IoDetail {
    checked: boolean;
    value: string;
}

export interface IoCheckboxEvent {
    detail: IoDetail;
}

export interface IoInputEvent {
    detail: string;
}

interface IoRadioDetail {
    value: string;
}

export interface IoRadioButtonEvent {
    detail: IoRadioDetail
}

export enum LicenseType {
    EVALUATION = 'EVALUATION',
    FULL = 'FULL',
    SUBSCRIPTION = 'SUBSCRIPTION',
    TOOL = 'TOOL'
}

export enum ProgressStatus {
    NONE,
    IN_PROGRESS,
    COMPLETED
}

export interface SearchQueryParams {
    page: number,
    pageSize?: number,
    sortBy?: string,
    query?: string,
    countries?: string,
    licenseTypes?: string,
    useCases?: string,
    industries?: string,
    companyUid?: string,
    hardwareRequirements?: string,
    packages?: string,
    licenses?: string,
    vehicleType?: string
}

export enum PaymentStatus {
    PAID = 'PAID',
    OVERDUE = 'OVERDUE',
    PENDING = 'PENDING',
    EXEMPT = 'EXEMPT',
    FAILED = 'FAILED'
}

export interface PurchasableLicenseData extends ProductLicenseData {
    quantity: number | string;
}

export interface Chip {
    text: string,
    closable?: boolean
}

export interface AppCardInterface {
    code: string,
    img: string,
    title: string,
    body: string,
    href?: string,
    url?: string,
    target?: string,
    subPrefix?: string,
    sub?: string,
    subAsLink?: boolean,
    subLinkHref?: string,
    chips?: Chip[],
    priceInfo?: ProductPriceDTO
}

export interface FollowAppChangesData {
    code: string,
    name: string,
    followAppData : FollowAppData
}

export interface CDNavigationItem {
    id?: string,
    text?: string,
    icon?: string,
    index?: number,
    href: string,
    target: string,
    highlight: boolean,
    hasDivider?: boolean
}

export interface EventNavigationItem {
    id?: string,
    text?: string,
    icon?: string,
    index?: number,
    onClick: () => void,
    highlight: boolean,
    hasDivider?: boolean
}

export interface HomeSwitcherItem {
    title: string,
    href: string,
    key: string
}

export interface HomeSwitcherItems {
    devcon: HomeSwitcherItem,
    dmt: HomeSwitcherItem
}

export interface AccordionData {
    title: string;
    content: string;
    contentUrl?: {
        label: string
        url: string
    }
}

export interface CreatePaymentInfoResponse {
    paymentInfoDraftCode: string;
    userActionParameters: { [key: string]: string; };
}

export interface FinalizePaymentInfoCreationResponse {
    paymentInfoDraftsToRetry: string[];
}

export interface ConfirmationDialogType {
    header?: string;
    bodyLines?: string[]
    buttonLabel?: string;
    cancelButtonLabel?: string;
}

export enum DiscountProductType {
    MASTER = 'master',
    THL = 'thl',
}

export type DiscountedPricesByProductCode = {
    [productCode: string]: DiscountedPriceData;
};

export interface DiscountedPriceData {
    licenseCode: string;
    price: number;
    discountPrice: number;
}

export interface DiscountProductPriceMap {
    master: DiscountedPriceData;
    thl: DiscountedPriceData;
}

export enum MandateStatus {
    DRAFT = 'DRAFT',
    ACTIVE = 'ACTIVE'
}

export interface SepaMandatePayload {
    accountHolderName: string;
    iban: string;
    useAsDefault: boolean;
    signatureDate?: string; // ISO date string (YYYY-MM-DD)
    companyId?: string;
    status?: MandateStatus;
}

export interface MandateData {
    accountHolderName?: string;
    iban?: string;
    mandateReference?: string;
    status?: MandateStatus;
}
