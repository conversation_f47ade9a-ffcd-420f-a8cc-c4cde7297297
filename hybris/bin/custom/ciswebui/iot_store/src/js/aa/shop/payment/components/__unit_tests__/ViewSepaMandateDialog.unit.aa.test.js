import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';
import ViewSepaMandateDialog from 'aa/shop/payment/components/ViewSepaMandateDialog.vue';

const defaultProps = {
  sepaDirectDebit: {
    accountHolderName: '<PERSON>',
    iban: '**********************',
    mandateReference: 'REF123',
    dateOfSignature: '2025-01-01',
  },
  creditor: '<PERSON>',
  creditorIdentifier: '******************',
  typeOfPayment: 'Recurring payment',
};

const mountComponent = (props = defaultProps) =>
  wrapperComponentFactory(ViewSepaMandateDialog, {
    props,
    shallow: false,
    global: {
      mocks: {
        $t: (msg) => msg,
        $d: (date) => {
          return new Date(date).toLocaleDateString();
        },
      },
      stubs: {
        'popup-dialog': {
          template: '<div><slot /></div>',
        },
      },
    },
  });

describe('ViewSepaMandateDialog', () => {
  it('should render the view dialog with creditor details and mandate information', () => {
    const wrapper = mountComponent();

    expect(wrapper.text()).toContain('shop.payment.sepaMandate.header');
    expect(wrapper.text()).toContain(defaultProps.creditor);
    expect(wrapper.text()).toContain(defaultProps.creditorIdentifier);
    expect(wrapper.text()).toContain(defaultProps.sepaDirectDebit.mandateReference);
    expect(wrapper.text()).toContain(defaultProps.typeOfPayment);
  });
});