import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';
import AddSepaMandateDialog from '../AddSepaMandateDialog.vue';

const defaultProps = {
    creditor: 'Robert <PERSON> GmbH',
    creditorIdentifier: 'DE12ZZZ00000010293',
    mandateReference: 'REF123',
    typeOfPayment: 'Recurring payment',
};

const mountComponent = (props = defaultProps) =>
    wrapperComponentFactory(AddSepaMandateDialog, {
        props,
        shallow: false,
    });

describe('AddSepaMandateDialog', () => {
    it('should render the dialog with header and key form elements', () => {
        const wrapper = mountComponent();

        expect(wrapper.text()).toContain('shop.payment.sepaMandate.header');
        expect(wrapper.find('[data-id="sepa-mandate-account-holder-name"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="sepa-mandate-iban"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="sepa-mandate-default-checkbox"]').exists()).toBeTruthy();
    });
});