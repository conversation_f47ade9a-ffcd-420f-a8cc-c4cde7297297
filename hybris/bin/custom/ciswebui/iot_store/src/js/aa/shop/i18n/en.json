{"create": "Create", "cancel": "Cancel", "delete": "Delete", "remove": "Remove", "close": "Close", "ok": "Ok", "or": "or", "by": "by ", "submit": "Submit", "back": "Back", "continue": "Continue", "payNow": "Pay now", "quantity": "Quantity", "total": "Total", "update": "Update", "select": "Select", "add": "Add", "edit": "Edit", "confirm": "Confirm", "send": "Send", "backendError": "Something went wrong. Please try again.", "default": "<PERSON><PERSON><PERSON>", "preference": "Preference", "new": "New", "save": "Save", "date": "Date", "language": {"en": "English", "de": "German", "fr": "French", "it": "Italian", "pt": "Portuguese", "hr": "Croatian", "sl": "Slovenian", "fi": "Finnish", "et": "Estonian", "lt": "Lithuanian", "lv": "Latvian", "sv": "Swedish", "no": "Norwegian", "da": "Danish", "es": "Spanish", "nl": "Dutch", "el": "Greek"}, "country": {"AE": "U.A.Emirates", "AR": "Argentina", "AT": "Austria", "AU": "Australia", "BE": "Belgium", "BG": "Bulgaria", "BR": "Brazil", "CA": "Canada", "CH": "Switzerland", "CL": "Chile", "CY": "Cyprus", "CZ": "Czech Republic", "DE": "Germany", "DK": "Denmark", "EE": "Estonia", "ES": "Spain", "FI": "Finland", "FR": "France", "GB": "United Kingdom", "GR": "Greece", "HR": "Croatia", "HU": "Hungary", "ID": "Indonesia", "IE": "Ireland", "IL": "Israel", "IN": "India", "IS": "Iceland", "IT": "Italy", "JP": "Japan", "KR": "Republic of Korea", "LT": "Lithuania", "LU": "Luxembourg", "LV": "Latvia", "MT": "Malta", "MX": "Mexico", "MY": "Malaysia", "NL": "Netherlands", "NO": "Norway", "NZ": "New Zealand", "PL": "Poland", "PT": "Portugal", "RO": "Romania", "SA": "Saudi Arabia", "SE": "Sweden", "SG": "Singapore", "SI": "Slovenia", "SK": "Slovakia", "TH": "Thailand", "TR": "Turkey", "TW": "Taiwan", "US": "United States of America", "VN": "Vietnam", "ZA": "South Africa"}, "api": {"error": {"product": {"notFound": "The product could not be found."}, "countryStore": {"failedToSwitchToDefault": "Failed to switch to default country store", "invalidCountryOrLanguage": "Invalid country or language"}}}, "navigation": {"loggedInAs": "Logged in as", "copyright": {"default": "Bosch | © 2023 Bosch Digital Commerce GmbH. All rights reserved.", "de": "© 2023 Robert Bosch GmbH. All rights reserved."}, "storeTitle": "Marketplace", "deviceManagementPortal": "License Management", "helpAndResources": "Help & Resources", "storePageTitle": "Marketplace", "getInTouch": "Kontakt", "languageSwitcher": {"dialog": {"title": "Region & Language", "selectLanguageLabel": "Select your preferred language", "selectCountryLabel": "Select your country/region", "setLanguageToDefault": "Set language to {default}", "visitMarketPlaceInCountry": "Visit marketplace in {country} ({language})", "confirm": "Confirm"}}, "headerExtensionTitle": "Mobility Aftermarket {country}", "items": {"deviceManagement": "License Management", "getInTouch": "Get In Touch", "globalHelpContact": "Contact Us", "globalHelpSupport": "Visit Help & Support", "globalImprint": "Corporate Information", "globalLegal": "Legal Note", "globalMyCompany": "My Company", "globalMyProfile": "My Profile", "globalSupport": "Support", "storeInfrigment": "Report Infringement", "storeLogin": "Log in", "storeOrderHistory": "Order History", "storePaymentDetails": "Payment Details", "storePrivacyPolicy": "Privacy Policy", "storeRegister": "Register", "storeSignOut": "Sign Out", "storeTermsAndConditions": "Terms and Conditions", "regionAndLanguage": "Region & Language", "marketplaceComponent": "Marketplace", "licenseManagementComponent": "License Management", "accountComponent": "Account", "storeRegionAndLanguageFooter": "Location", "privacySettings": "Privacy Settings"}, "breadcrumbs": {"CartPage": "<PERSON><PERSON>", "CheckoutConfirmationPage": "Confirmation", "PendingCheckoutConfirmationPage": "Pending Confirmation", "DelayedCheckoutConfirmationPage": "Confirmation", "HomePage": "Marketplace", "OrderDetailsPage": "Order {code}", "OrderHistoryPage": "Order history", "PaymentDetailsPage": "Payment details", "ProductDetailsPage": "Product details", "ProductsSearchPage": "Search & filter", "UpdatePaymentMethodPage": "Update Payment Method"}}, "shop": {"reviews": {"anonymous": "Anonymous"}, "license": {"evaluation": {"name": "Trial"}, "full": {"name": "Purchase"}, "subscription": {"name": "Subscription"}}, "notification": {"accountUnderReview": {"title": "Account under review", "summary": "Your account is currently under review. You can already place orders, that will be processed and finalized once your account is ready.", "description": {"paragraph": "Your account is still being reviewed by our team.", "licenses": "Checks are still running and licenses for orders you place in the meanwhile will be issued once review is complete", "payments": "Payments for the orders you place will only be executed once account review renders successful, otherwise no money will be deducted from your payment method", "orders": "The review can take between 1-2 business days to be completed. Orders placed within this time will be automatically processed once review is done."}}}, "products": {"header": "ESI[tronic] Diagnostic software", "title": "Products", "searchProducts": "Search for products", "searchResults": "Search results for", "noSearchResults": "Sorry! We didn't find any matching products. Let us know what you are looking for. {url}", "contactUs": "Contact us.", "loadMore": "Load more", "buyButton": "Purchase", "buyButtonWithPrice": "Purchase from {currency} {price}", "buyButtonWithPriceFrequency": "Purchase from {currency} {price} {frequency}", "priceWithFrequency": "from {currency} {price} {frequency}", "price": "from {currency} {price}", "perYear": "p.a.", "sorting": {"prefix": "Sort by", "created": "Newest", "acquisitionCount": "Most purchased", "relevance": "Most relevant"}, "facets": {"availability": {"country": "Available in your country", "trial": "Trial", "privateapps": "Your private products"}, "group": {"availability": {"name": "Availability"}, "hardwarerequirements": {"name": "Required Hardware"}, "packages": {"name": "Packages"}, "licenses": {"name": "License Type"}, "vehicletype": {"name": "Vehicle type"}, "licenseruntimes": {"name": "License type", "values": {"runtime_subs_unlimited": "Subscription", "runtime_full_3y": "3-year contract", "runtime_full_unlimited": "One time purchase"}}, "firstlevelcategories": {"name": "Category"}}, "compatibleHardwareDialog": {"title": "Compatible Hardware", "searchHardware": "Search by hardware name...", "confirmButton": "Confirm", "cancelButton": "Cancel"}, "showMore": "Show more", "filters": "Filters", "filteredBy": "Filtered by", "filteredSearchResults": "Filtered search results", "filteredSearchResultsFor": "Filtered search results for "}}, "productsOverview": {"pageTitle": "Products Overview", "header": {"title": "Welcome to the marketplace for Bosch Mobility Aftermarket Software"}, "navigation": {"searchByHardware": "Search & filter"}, "category": {"product": {"purchaseButton": "Buy now", "price": "from {currency} {price}", "licenseTypesAndTerms": "License types & terms"}, "otherCompatibleHardwareDialog": {"activator": "+ 1 other | + {n} others", "title": "Hardware Compatibility", "listDescription": "The selected software is compatible with the following devices:", "closeButton": "Close"}, "packetComparisonDialog": {"activator": "Compare {name} packages", "title": "Compare packages", "description": "For further details please click on the desired function.", "optional": "optional", "optionalReference": "optional*", "optionalHint": "*optional: Can be booked for an extra charge", "buyButton": "Buy"}}}, "productSelection": {"pageTitle": "Product Selection", "header": {"subtitle": "Your configuration for"}, "variantSelection": {"runtime": {"runtimeType": "License Type", "name": {"runtime_subs_unlimited": "Subscription", "runtime_full_3y": "3-year contract", "runtime_full_unlimited": "One time purchase"}, "descriptionDetailsMap": {"runtime_subs_unlimited": {"1": "Billed Annually", "2": "License(s) renew yearly (unless canceled up to 56 days before renewal)", "3": "Including updates"}, "runtime_full_3y": {"1": "Pay once", "2": "License(s) are valid for 3 years", "3": "Including updates"}, "runtime_full_unlimited": {"1": "Pay once", "2": "Not including updates"}}}, "variant": {"titleBundles": "License bundle", "titleNonBundles": "License amount", "pricingSection": {"price": "{currency} {price}", "perYear": "p.a.", "addButton": "Add", "tooltipText": "This licence is not available for purchase.", "importedCompanyTooltipText": "Purchasing is temporarily unavailable while this company is undergoing migration."}, "bundle": {"toolTipText": "The software can be installed on one device. | The software can be installed on a maximum of {n} devices of a workshop."}}}, "summary": {"summary": "Summary", "license": "1 license | {n} licenses", "bundle": "{quantity}x {<PERSON><PERSON><PERSON><PERSON>}", "addToCart": "Add to cart", "nothingSelectedInfo": "To proceed, please add one or more license bundles and, if desired, select additional matching products.", "preliminarySums": "When purchasing {productName} together with {bundleProductName}, you receive a special discount of {discountPercentage}%.", "price": "{currency} {price}", "perYear": "p.a.", "addToCartSuccess": "Successfully added to your cart.", "addToCartGeneralError": "An error occurred. The product could not be added to the cart."}}, "thl": {"notification": "When purchasing {productName} with {bundleProductName}, you receive a special discount", "title": "Technical Hot Line", "description": "When purchasing {productName} you receive {bundleProductName} bundled. It is not possible to delete or edit the license amount for {bundleProductName} independently.", "popup": {"header": "We have a special offer for you", "body": {"line1": "When purchasing {productName} together with {bundleProductName}, you receive a special discount of {discountPercentage}%.", "line2": "If you accept this offer {bundleProductName} will be bundled with your {productName} configuration at a discounted price."}, "button": {"confirmation": "Accept offer", "cancel": "Refuse discount"}}}, "gridItem": {"notAvailableLabel": "Not available in your country", "notAvailableLabelShort": "Not available", "readOnlyLabel": "Coming soon", "privateAppLabel": "Private product"}, "productDetails": {"productHeader": {"trialButton": "Try for free", "tryFreeButton": "Try it free", "purchaseButton": "Buy now", "getFreeToolButton": "Get free tool", "byCompany": "By", "applicationStore": "Marketplace", "noLicense": "Check out this product. It will be available soon.", "maintenance": {"title": "We are performing a scheduled maintenance.", "subtitle": "The purchase functionality is not available at this moment."}, "privateAppLabel": "Private product"}, "gallerySection": {"title": "Preview"}, "packageDetailsSection": {"title": "Package in detail", "addons": "Related add-ons", "video": "View video"}, "pricingSection": {"addToCart": "Add to cart", "addToCartSuccess": "Successfully added to your cart.", "addToCartSuccessWithDiscount": "Successfully added to your cart. {percent}% volume discount has been applied.", "addToCartGeneralError": "An error occurred. The product could not be added to the cart.", "pricing": "Pricing", "quantity": "Quantity", "permissionText": "You do not have permission to purchase applications.", "countryPermissionText": "This product is not available in your country", "integratorCountryBlockedText": "This product's commercial licenses are blocked in your country", "subscriptionPermissionText": "The subscriptions can currently only be ordered with the bank transfer payment method. For details, please contact our customer support.", "learnMore": "Learn more", "free": "Free", "pricetext": "one time", "perYear": "per year", "fullLicenseName": "Purchase", "fullLicenseSummary": "One purchased app can be installed on one camera and does not expire.", "evaluationLicenseName": "Trial", "evaluationLicenseSummary": "One app trial can be installed on one camera and will expire after 30 days.", "anonymousUserInfo": "{url} to see the pricing and test it free.", "logIn": "Log in", "trialNowFullSoon": "The marketplace is not yet available in your country.", "quantityInvalid": "The maximum order quantity of an individual product is {0}.", "license": {"evaluation": {"name": "Trial", "summary": "Try the product for free", "detailsMap": {"1": "I confirm that I agree with the terms of use for this product"}}, "full": {"name": "Purchase", "summary": "Get the product with a permanent license", "detailsMap": {"1": "One time purchase for a permanent license", "2": "One purchase can be only installed and used on one device at one time"}}, "subscription": {"name": "Subscription", "summary": "Get the product on a subscription basis, billed every 12 months.", "detailsMap": {"1": "The contract period begins on the day of the order and is for 1 year.", "2": "The subscription automatically renews for another year if termination is not received no later than 8 weeks before the end of the respective contract period.", "3": "A separate license is required for each device."}, "futurePriceText": "The subscription price will change to {futurePrice} per 12 months after the first year due to a price change from {startDate}."}}, "volumeDiscounts": {"table": {"headings": {"quantity": "Quantity", "discount": "Discount", "pricePerApp": "Price per item"}}, "callout": "Save up to {num}% purchasing in bulk"}}, "descriptionSection": {"label": "About"}, "documentsSection": {"title": "Documents"}, "supportSection": {"title": "Support", "phone": "Phone", "email": "Email", "website": "Website"}, "sidebar": {"version": "Current Version", "legalCompanyName": "Legal company name", "companyWebsite": "Company website", "productId": "Product ID", "partnumber": "Part number", "privacyPolicyApp": {"title": "Privacy policy", "description": "Bosch Digital Commerce GmbH's privacy policy does not apply to this product. Please refer to the application's privacy policy.", "link": "Application's privacy policy"}, "termsOfUseApp": {"title": "License terms", "link": "End User License Agreement for this Product", "appendix": "Appendix A: 3rd party software licenses", "standardMark": "Standard"}, "countries": {"title": "Available in"}, "privateOffer": {"title": "Request custom offer", "description": "Reach out to the seller of this app to request custom conditions for your purchase.", "button": "Contact seller"}}, "reviewSection": {"title": "Reviews", "notReviewed": "This app has not been reviewed yet.", "addReview": "Add review", "wantToAddReview": "Add review", "shareReviewTeaser": "Share your experience with this app.", "buyApp": "Once your company has bought this app, you will be able to leave reviews."}, "dualUseInfo": {"header": "Export information", "linkText": "View all products and their export control classification."}}, "checkout": {"header": "Secure Checkout", "pageTitle": "Check out – ", "finalReview": "Final Review", "cardHolder": "Card Holder", "cardName": "Name on Card", "cardNumber": "Card Number", "cardExpiry": "Expiry Date", "cardCvc": "Security Code", "saveCard": "Save Payment Info", "wrongValue": "The value is invalid", "wrongCardName": "The cardholder name length must be 2 to 26 characters including first name, last name and spaces.", "invoiceNote": {"title": "Add internal notes to the order document", "description": "Provide additional information such as internal purchase order number and project name. The notes will be displayed on the invoice.", "noteLine": "Your reference number - Line {line} (optional)"}, "distributor": {"title": "Distributor", "description": "Select your distributor to be the responsible for workshop test equipment. Selection will apply to this order only.", "change": "Select distributor", "dialog": {"title": "Select distributor", "description": "Select your distributor to be the responsible for workshop test equipment. Selection will apply to this order only."}, "notSelected": "Please select the distributor to proceed with the purchase."}, "orderEntries": {"futurePrice": "afterwards {futurePrice} per 12 months"}, "miniSummary": {"headerAddress": "Billing Address", "ownPurchaseHeader": "Billing email", "totalPrice": "Total net price", "taxNote": "Tax is specified on the invoice", "taxNoteInvoiceBySeller": "Invoicing including tax calculation and payment process will be managed by seller.", "taxHint": "An additional amount will be reserved on your credit card to cover applicable taxes. After tax calculation, the invoiced gross amount will be captured from your credit card."}, "backToCart": "Back to Cart", "newCard": "New Credit Card", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "placeOrderAndPay": "Place order and pay", "paymentMethodNotSavedHint": "This payment method will only be saved in your account after purchase.", "paymentMethodOverwriteHint": "Adding a further payment method will replace your previously temporarily added payment method.", "placeOrderConfirmation": "By placing this order I agree with the terms of use for the purchased products.", "deletePaymentMethod": "Delete payment method?", "error": "Something went wrong with your order.", "byCompany": "By", "confirmation": {"confirmationHeader": "Confirmation", "confirmationLine1": "Your purchase has been successfully processed.", "confirmationLine2": "It may take a few minutes before your purchase appears in your products list.", "licenseActivation": "Subscription will only start counting after your licenses are activated", "confirmationLineInvoice": "The invoice with payment information will be made available to you in your account shortly.", "confirmationLine3": "Order number", "confirmationLine3plural": "Order numbers", "continueShopping": "Continue Shopping", "goToLicenseActivation": "Go to license activation", "viewLicenseManagement": "View License Management", "checkInboxHeader": "Check your inbox", "checkInboxText1": "An email with detailed information about your order has been sent to {buyerEmail}. Licenses will be made available via License management for assignment and installation via DDM.", "checkInboxText2": "To download licenses via DDM, please look for your log in credentials sent to your licensing email {licensingEmail}.", "orderPlaced": "Order placed", "accountUnderReview": {"confirmationLine1": "Your order has been successfully placed.", "confirmationLine2": "The final processing of the order will happen, once your account review was successful. This may take up to 3 business days."}, "paymentAndInvoice": {"header": "Payment and invoice", "text": "Payment collection and invoicing will only happen after license activation."}}, "orderPlaced": {"line3": "Order number: {orderNumber}"}, "summary": {"header": "Whats next?", "pendingConfirmationText": "Once the account review was successful, the order will be  automatically processed and the invoice generated. A confirmation will be sent to {companyEmail}. For the subsequent installation of the products via Diagnostic Download Manager (DDM), log in credentials will be sent to your licensing email {licensingEmail}.", "delayedConfirmationText": "This order contains one or more licenses that have not yet been activated. This step is needed so licenses are only running once you have your ESI[tronic] hardware available. You can activate all licenses via {licenseManagement} portal."}}, "cart": {"cart": "<PERSON><PERSON>", "pageTitle": "<PERSON><PERSON> – ", "emptyCartMessage": "Your shopping cart is empty", "checkout": "checkout", "continueShopping": "Continue Shopping", "orderFrom": "Order from ", "subscriptionTitle": "Subscription"}, "cartTotal": {"total": "Net Price", "taxmessage": "* Your order does not include taxes", "tax": "Mwst."}, "bundleInfo": {"description": "{bundleName} ({bundleSize})", "size": "1 license | {n} licenses"}, "cartItem": {"itemRemovedSuccessMessage": "The item was removed from your cart.", "itemRemovedErrorMessage": "The item could not be removed from your cart. Please reload the page and try again.", "messageOnUpdateCart": "Your cart was updated successfully.", "messageOnUpdateCartWithDiscount": "Your cart was updated successfully. {percent}% volume discount has been applied.", "updateCartGeneralError": "An error occurred. The item quantity could not be updated.", "messageOnUpdateCartFailed": "The requested product was already removed from the cart.", "volumeDiscount": "Volume discount", "hint": "Buy {min}+ and get {percent}% off each", "per12Months": "p.a.", "for12Months": "for 12 months", "eulaAcceptanceText": "Accept software license agreement:"}, "exportInformation": {"title": "Global trade compliance", "pageTitle": "Global trade compliance – ", "appName": "Product name", "companyName": "<PERSON><PERSON><PERSON>", "eccn": "ECCN"}, "orderStatus": {"progress": "In Progress", "completed": "Completed", "pending": "Payment pending", "confirmed": "Order confirmed", "overdue": "Payment overdue", "refunded": "Refunded", "partially_refunded": "Partially refunded", "awaiting_license_activation": "Activation pending", "on_validation": "Order pending", "cancelled": "Cancelled", "rejected": "Rejected"}, "orderHistory": {"title": "Order history", "pageTitle": "Order history – ", "order": "Order", "table": {"orderNumber": "Order number", "apps": "Products", "placedBy": "Placed by", "orderDate": "Order date", "lastUpdated": "Last updated", "status": "Status"}, "noOrders": {"title": "No orders", "text": "If there are any placed orders, you'll find them listed here."}, "creditCardPaymentSuccess": "Credit card payment method successfully updated for the following orders: "}, "orderDetails": {"items": {"by": "By", "quantity": {"label": "Quantity", "tooltipText": "Quantity when the order was placed"}}, "orderInfo": {"label": "Order details", "placedDate": "Date", "placedBy": "Placed by", "payment": {"method": {"label": "Payment Method", "invoiceBySeller": "Managed By Seller", "sepaCredit": "SEPA Credit Transfer", "achInternational": "ACH Credit Transfer", "creditCard": "Credit Card", "sepaDirectdebit": "SEPA Direct Debit"}}, "paymentFailed": "There is a problem with your card ending with {ccEnding}.", "update": "Update"}, "invoices": {"label": {"default": "Invoices", "documents": "Documents"}, "table": {"headers": {"invoiceId": {"label": {"default": "Invoice ID", "document": "Document ID"}}, "invoiceDate": {"label": {"default": "Invoice date", "document": "Date"}}, "invoiceStatus": {"label": "Status"}, "totalAmount": {"label": {"default": "Total amount", "document": "Total net amount"}, "tooltipText": "Including taxes"}}, "data": {"invoiceStatus": {"paid": "Paid", "completed": "Completed", "activationPending": "Activation pending", "exempt": "Exempt", "pending": "Pending", "overdue": "Overdue", "notPaid": "Not paid", "reversed": "Reversed", "refunded": "Refunded", "partiallyRefunded": "Partially refunded", "paymentFailed": "Payment failed", "accountUnderReview": {"pending": "Order pending"}}, "documentIssuedDate": {"label": "{documentType} document issued"}, "documentType": {"invoice": "Invoice", "reversal": "Reversal", "refund": "Refund", "partialRefund": "Partial refund", "transactionReport": "Transaction report"}, "accountUnderReview": {"tooltipText": "Order waiting for account to be reviewed. Once is done, this order will be processed and finalized.", "invoiceWilBeIssued": "Invoice will be issued once your account review is concluded"}}}}}, "payment": {"tooltip": {"bankTransferDisabled": "Payment by bank transfer is not enabled for your company. For details, contact the customer support.", "generalInfo": "An order document will be available after placing the order.", "overValue": "This order exceeds your company credit limit for SEPA Credit Transfer. For details, please contact our customer support.", "invoicebyseller": "Due to local Korean tax requirements, the seller will manage invoicing and payment process.", "companyScope": "Company-wide payment method. Any user with purchase permission in your company account can view and use this payment method."}, "notEnabled": "Not enabled", "removed": "Payment method was successfully removed.", "paymentDetailsHeader": "Payment details", "pageTitle": "Payment details – ", "savedPaymentMethods": "Saved payment methods", "creditCard": "Credit card", "bankTransfer": "Bank transfer", "directDebit": "Direct debit", "noPaymentDetails": "", "noPaymentDetailsInfo": "You don’t have saved any payment methods yet. You can add and save new payment methods when purchasing apps.", "sepaTransfer": "SEPA Credit Transfer", "sepaDirectDebit": "SEPA Direct Debit", "sepaMandateForAccount": "SEPA Direct Debit for Account", "createDirectDebitPaymentInfo": "Add SEPA direct debit mandate", "sepaMandateNew": "New SEPA Direct Debit Mandate", "mandateReference": "Mandate Reference", "dateOfSignature": "Date of Signature", "achInternational": "ACH Credit Transfer", "transferTo": "Transfer to", "accountName": "Account Holder", "account": "Account", "bankName": "Bank Name", "iban": "IBAN No.", "bicSwift": "BIC/Swift No.", "routing": "Routing No.", "accountNumber": "Account No.", "invoicebyseller": "Managed By Seller", "setPreference": "Set as default", "removePreference": "Unset as preference", "delete": "Delete", "setPreferenceSuccess": "The payment method is successfully set as preference.", "removePreferenceSuccess": "The payment method is successfully removed as preference.", "paymentRemoved": "The payment method was successfully removed.", "deletePaymentMethod": "Delete payment method?", "removePaymentOption": "Remove payment option", "viewPaymentOption": "View mandate details", "removePaymentOptionInfo": "Removing this payment method disables it for new purchases but keeps it for existing subscriptions.", "deletePaymentMethodInfo": "This payment method will be deleted. You can add new payment methods when purchasing apps.", "settingPreferenceFailed": "The payment method could not be set as preference. Please try again later or contact customer support.", "billingAddress": "Billing address", "billingAddressInfo": "Your company’s billing address. To change it {url}.", "contactCustomerSupport": "contact the customer support", "waitForPaymentInfoConfirmation": "Your payment data is bering verified. Please wait a moment.", "sepaMandate": {"header": "SEPA Direct Debit Mandate", "accountHolderName": "Account Holder Name", "iban": "IBAN", "mandateReference": "Mandate Reference", "submit": "Submit Mandate", "defaultPaymentCheckbox": "Use this as default payment method for future purchases", "creditor": "Creditor", "creditorIdentifier": "Creditor identifier", "typeOfPayment": "Type of payment", "recurringPayment": "Recurring payment", "legalText": "By providing your payment details and confirming this payment, you authorize (A) {beneficiary} and {paymentProvider}, our payment service provider, to send instructions to your bank to debit your account and (B) your bank to debit your account by the payment instruction. You can request a refund within 8 weeks of the debit date. The terms you agreed to with your bank apply. Your rights are explained in a statement that you can obtain from your bank.", "description1": "You have authorized (A) {beneficiary} and {paymentProvider}, our payment service provider, to send instructions to your bank to debit your account and (B) your bank to debit your account by the payment instruction. ", "description2": "You can request a refund within 8 weeks of the debit date. The terms you agreed to with your bank apply. Your rights are explained in a statement that you can obtain from your bank.", "description3": "The terms you agreed to with your bank apply. Your rights are explained in a statement that you can obtain from your bank.", "defaultTooltip": "We will make this your default payment method for future purchases during your checkout experience.", "error": {"invalidIban": "Invalid IBAN. Please check the format", "invalidMandateReference": "Mandate reference must be 1-35 characters long and contain only uppercase letters, numbers, and allowed special characters (no spaces or umlauts).", "accountHolder": "Please enter a valid account holder name", "ibanRequired": "Please enter a valid IBAN", "mandateReferenceRequired": "Please enter a valid Mandate Reference", "submissionFailed": "Error submitting mandate", "invalidInput": "The data you entered is invalid. Please review the form and try again", "notFound": "No SEPA Mandate found", "backendError": "Something went wrong. Please try again later"}, "success": "Mandate submitted successfully!"}}, "updatePayment": {"header": "Update Payment Method", "subtitle": "Please select a different payment method or add a new card.", "newCard": "New Credit Card", "finalReview": "Final Review", "cardHolder": "Card Holder", "cardName": "Name on Card", "cardNumber": "Card Number", "cardExpiry": "Expiry Date", "cardCvc": "Security Code", "saveCard": "Save as preferred card and update for all past due/future orders", "wrongValue": "The value is invalid", "wrongCardName": "The cardholder name length must be 2 to 26 characters including first name, last name and spaces.", "problemWithPayment": "There is a problem with your credit card ending with {ccEnding}.", "placeOrder": "Place Order", "placeOrderAndPay": "Place order and pay", "paymentMethodNotSavedHint": "This payment method will only be saved in your account after purchase.", "paymentMethodOverwriteHint": "Adding a further payment method will replace your previously temporarily added payment method.", "placeOrderConfirmation": "By placing this order I agree with the terms of use for the purchased apps.", "deletePaymentMethod": "Delete payment method?"}, "tools": {"pageTitle": "Tools – ", "header": {"title": "<PERSON><PERSON><PERSON>", "description": "Find free tools to empower your system"}, "container": {"title": "<PERSON><PERSON><PERSON>"}}, "companyProfile": {"companyInfo": {"founded": "Founded", "size": "Company size", "headquarters": "Headquarters", "visitWebsite": "Visit website", "visitLinkedinProfile": "Visit Linkedin profile", "about": "About"}, "contactInfo": {"sales": "Sales contact", "support": "Support", "phone": "Phone", "email": "E-mail", "supportPage": "Support page"}, "apps": {"title": "Apps", "noAppsFound": "No apps found."}}, "error": {"backend": "Something went wrong. Please try again.", "validation": "Some of your inputs are invalid.", "generic": {"serverError": "Something went wrong. Please try again.", "notFound": "The resource could not be found.", "badRequest": "The request was invalid and could not be processed.", "forbidden": "You do not have sufficient permissions to access the requested resource."}, "review": {"exists": "An employee of your company has already reviewed this app.", "appNotOwned": "You have to purchase the app in order to review it."}, "checkout": {"cartModified": "Your cart was modified. Please verify the changes and proceed to checkout one more time.", "placeOrderFailed": "Failed to place the order.", "paymentFailed": "There was an error with your payment. Please try again or use a different payment method.", "invalidCartState": "A technical error occurred, please try again later. Should the issue persist, please contact our customer support."}, "cart": {"unpayable": "Could not determine any suitable payment method for your cart. Please reduce the total amount of the cart.", "empty": "You cannot proceed to checkout with an empty cart.", "invalidQuantity": "The maximum order quantity of an individual app is {0} for permanent licenses, and {1} for subscription licenses.", "noSelfPurchases": "You can’t purchase apps from your own company.", "addFailed": "An error occurred while adding to cart.", "unsupportedLicense": "Product could not be added to the cart.", "preparationFailure": "A technical error occurred while preparing the checkout for your cart, please try again later. Should the issue persist, please contact our customer support."}, "licenseactivation": {"generic": "Error when activating license.", "notPurchasable": "License activation failed. License is not purchasable.", "notSupported": "License activation failed. License type is not supported."}, "companyprofile": {"notFound": "Company profile could not be found."}, "product": {"notFound": "The product could not be found."}, "eula": {"notAccepted": "Please accept the software license agreements before placing the order"}, "paymentInfo": {"creationFailure": "Payment method creation failed.", "creationCanceled": "Payment method creation canceled."}}, "storeEditSuccessResponseMessage": "The data was saved successfully", "contactCustomerSupport": "Contact customer support", "infoBanner": {"text": "You are currently viewing a different store region. To purchase licenses you must return to the region your account is registered to.", "buttonText": "Return to region"}, "purchaseDisabledBanner": {"text": "Purchases are temporarily unavailable due to contract migration and will become available on {date}."}, "directDebitCtaBanner": {"text": "For migrating your existing contracts you need to update your payment method. To do so, simply click on the button or go to payment details page.", "buttonText": "Provide SEPA Direct Debit mandate"}, "loginCtaBanner": {"text": "In order to purchase or get products assigned to you it is necessary to have a registered account."}, "managedAccountBanner": {"text": "You are currently using a managed account. To buy products, please get in contact with one of your distributors, they can purchase new products and assign licenses to your account."}, "runtime": {"runtime_subs_unlimited": "Subscription", "runtime_full_3y": "3-year contract", "runtime_full_unlimited": "One time purchase"}, "specialOffer": {"chip": {"label": "Special offer"}, "conditions": {"title": "Special offer conditions", "body": "* The displayed price is a limited-time discount applicable for the initial year of the subscription. Following this period, {0} or previously agreed-upon prices will take effect.", "link": "standard list prices"}}}}