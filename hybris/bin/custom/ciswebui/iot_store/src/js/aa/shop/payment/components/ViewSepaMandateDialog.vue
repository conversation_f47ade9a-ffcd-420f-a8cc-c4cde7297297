<template>
  <popup-dialog
    v-bind="$attrs"
    v-on="$attrs"
    class="sepa-mandate-dialog ma-0"
    data-id="view-sepa-mandatepopup-dialog"
    :header-close="true"
    :close-button="{ text: t('close') }"
    @close="closeDialog"
  >
    <template #header>
      {{ t('shop.payment.sepaMandate.header') }}
    </template>

    <v-container class="mt-4 px-0">
      <CDBlock :border-bottom="true"
               :padding-all="false"
               :padding-bottom="true"
               class="mb-8 pb-4"
      >
        <p class="mb-4">
          {{ t('shop.payment.sepaMandate.description1', {
                paymentProvider: props.sepaDirectDebit?.paymentProvider,
                beneficiary: creditor
              }) }}
        </p>
        <p class="mb-4">
          {{ t('shop.payment.sepaMandate.description2') }}
        </p>
        <p class="mb-4">
          {{ t('shop.payment.sepaMandate.description3') }}
        </p>
      </CDBlock>
      <CDBlock :border-bottom="true"
               :padding-all="false"
               :padding-bottom="true"
               class="mb-8 pb-4"
      >
        <v-row>
          <v-col cols="12">
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.accountHolderName') }}:
              </h5>
              <span>{{ props.sepaDirectDebit?.accountHolderName }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.iban') }}:
              </h5>
              <span>{{ maskedIban }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.mandateReference') }}:
              </h5>
              <span>{{ props.sepaDirectDebit?.mandateReference }}</span>
            </div>
            <v-row>
              <v-col cols="7">
                <div class="mb-4">
                  <h5 class="d-inline-block mr-2">
                    {{ t('shop.payment.sepaMandate.typeOfPayment') }}:
                  </h5>
                  <span>{{ typeOfPayment }}</span>
                </div>
              </v-col>
              <v-col cols="5">
                <div class="mb-4">
                  <h5 class="d-inline-block mr-2">
                    {{ t('date') }}:
                  </h5>
                  <span>{{ $d(new Date(sepaDirectDebit?.dateOfSignature), 'short') }}</span>
                </div>
              </v-col>
            </v-row>
            </v-col>
          </v-row>
      </CDBlock>
      <CDBlock :border-none="true"
               :padding-all="false"
               :padding-bottom="false"
               class="mb-8"
      >
        <v-row>
          <v-col cols="12">
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditor') }}:
              </h5>
              <span>{{ creditor }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditorIdentifier') }}:
              </h5>
              <span>{{ creditorIdentifier }}</span>
            </div>
          </v-col>
        </v-row>
      </CDBlock>
    </v-container>
  </popup-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n-bridge';

import { PopupDialog } from 'common/components/popups';
import { SepaMandatePaymentInfoData } from 'common/generated-types/types';

const props = defineProps<{
  sepaDirectDebit: SepaMandatePaymentInfoData;
  creditor: string;
  typeOfPayment: string;
  creditorIdentifier: string;
}>();

const emit = defineEmits<{
  (e: 'cancel'): void;
}>();

const { t } = useI18n();

function maskIban(iban: string): string {
  if (iban?.length <= 4) return iban;
  const visible = 4;
  const hiddenPart = '*'.repeat(iban?.length - visible);
  const visiblePart = iban?.slice(-visible);
  return hiddenPart + visiblePart;
}

const maskedIban = computed(() => maskIban(props.sepaDirectDebit?.iban));

const closeDialog = () => {
  emit('cancel');
};
</script>

<style scoped>
  p,
  span,
  h5 {
    color: var(--v-grey-darken3);
  }
</style>